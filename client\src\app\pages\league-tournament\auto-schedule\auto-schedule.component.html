<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- content-header component -->
    <app-content-header [contentHeader]="contentHeader"></app-content-header>
    <div class="top-header">
      <div class="row mb-1">
        <!-- ng select season -->
        <div class="d-flex flex-column" style="gap: 2px">
          <label for="selectDate">{{ 'Date' | translate }}</label>
          <ng-select
            id="selectDate"
            [searchable]="true"
            [clearable]="false"
            placeholder="{{ 'Select Date' | translate }}"
            [(ngModel)]="selectedDate"
            (change)="onSelectDate($event)"
            style="min-width: 200px"
          >
            <ng-option *ngFor="let date of dateOptions" [value]="date"
              >{{ date }}
            </ng-option>
          </ng-select>
        </div>
        <div class="d-flex align-items-center" style="gap: 8px">
          <button
            class="btn btn-primary"
            (click)="openModalSetupSchedule()"
            [disabled]="isLock"
          >
            <i data-feather="settings" class="mr-25"></i>
            Add Schedule
          </button>
          <button
            class="btn btn-icon"
            (click)="onClickLock()"
            [ngClass]="{
              'btn-outline-primary': !isLock,
              'btn-primary': isLock
            }"
          >
            <i
              [ngClass]="isLock ? 'fa-regular fa-lock' : 'fa-regular fa-unlock'"
            ></i>
            <!--                        {{ isLock ? 'Unlock' : 'Lock' }}-->
          </button>
          <button
            class="btn btn-icon btn-outline-danger"
            (click)="clearSchedule()"
            [disabled]="isLock"
          >
            <i data-feather="trash-2"></i>
            <!--                        Clear Schedule-->
          </button>
        </div>
      </div>
    </div>
    <div cdkDropListGroup>
      <div class="col-9" id="listLocationZone">
        <div class="horizontal-scroll-container" *ngIf="hasPlan">
          <div class="location-columns-wrapper">
            <ng-container *ngFor="let locationKey of listLocationIds">
              <div class="location-column mb-2">
                <div class="bg-white shadow-sm">
                  <header class="location-header">
                    <div
                      class="d-flex align-items-start justify-content-between"
                    >
                      <p class="location-name h4">
                        {{ locationKey }}
                      </p>
                      <div class="" ngbDropdown *ngIf="!isLock">
                        <button
                          class="btn btn-link dropdown-toggle"
                          ngbDropdownToggle
                        >
                          <i class="fa-solid fa-ellipsis-vertical"></i>
                        </button>
                        <div
                          ngbDropdownMenu
                          aria-labelledby="dropdownMenuButton"
                        >
                          <a
                            ngbDropdownItem
                            (click)="
                              editSchedule(
                                locationKey,
                                getConfig(
                                  responseMetadata['locations'][locationKey].id
                                )?.id
                              )
                            "
                          >
                            Edit Plan
                          </a>
                          <a ngbDropdownItem (click)="deletePlan(locationKey)">
                            Delete Plan
                          </a>
                        </div>
                      </div>
                    </div>
                    <p class="location-stage-name">
                      {{
                        getShortTime(
                          getConfig(
                            responseMetadata['locations'][locationKey].id
                          )?.begin_date +
                            ' ' +
                            getConfig(
                              responseMetadata['locations'][locationKey].id
                            )?.begin_time || ''
                        )
                      }}
                    </p>
                  </header>
                  <div
                    class="dnd-zone"
                    cdkDropList
                    [cdkDropListData]="listMatches[locationKey]"
                    (cdkDropListDropped)="drop($event)"
                    [id]="locationKey"
                    [data_location_id]="
                      responseMetadata['locations'][locationKey].id
                    "
                  >
                    <div
                      *ngFor="let item of listMatches[locationKey]"
                      class="dnd-item location-match-row"
                      [ngClass]="{
                        'conflict-border': isMatchHasConflict(item.id, 'match')
                      }"
                      cdkDrag
                      [data_stage_id]="item.stage_id"
                      [data_time_slot_id]="item.time_slot_id"
                      [data_type]="item.type"
                      [cdkDragDisabled]="isLock"
                      (click)="!isLock && toggleDropdown($event, item)"
                    >
                      <button
                        type="button"
                        rippleEffect
                        class="conflict-tooltip btn btn-link"
                        placement="right"
                        container="body"
                        ngbTooltip="This match has conflict"
                        *ngIf="isMatchHasConflict(item.id, 'match')"
                      >
                        <i
                          class="fa-light fa-circle-exclamation"
                          style="font-size: 16px"
                        ></i>
                      </button>

                      <ng-container *ngIf="item.type === 'match'">
                        <ng-container
                          *ngTemplateOutlet="
                            matchScheduledTemplate;
                            context: { $implicit: item }
                          "
                        ></ng-container>
                      </ng-container>
                      <ng-container *ngIf="item.type === 'break'">
                        <div class="break-info-header">
                          <p class="text-center m-0">
                            {{ item.description || 'Break time' }}
                          </p>
                        </div>
                        <div class="break-row">
                          <i class="fa-regular fa-clock" aria-hidden="true"></i>
                          <p class="break-time m-0">
                            {{ item.break_durations }} mins
                          </p>
                        </div>
                      </ng-container>

                      <!-- Individual Dropdown for this item -->
                      <div
                        class="item-dropdown"
                        [class.visible]="isDropdownOpen(item)"
                      >
                        <div class="dropdown-content">
                          <!-- Match type dropdown options -->
                          <ng-container *ngIf="item.type === 'match'">
                            <button
                              class="dropdown-item"
                              (click)="onEditMatch(item)"
                            >
                              <i
                                class="fa-regular fa-whistle mr-2"
                                style="rotate: -45deg"
                              ></i>
                              {{ 'Update Match Referees' | translate }}
                            </button>
                            <button
                              class="dropdown-item"
                              (click)="
                                onUnscheduleTimeSlot(
                                  item,
                                  getConfig(
                                    responseMetadata['locations'][locationKey]
                                      .id
                                  )?.id
                                )
                              "
                            >
                              <i class="fa fa-trash mr-2"></i>
                              {{ 'Unschedule Match' | translate }}
                            </button>
                          </ng-container>

                          <!-- Break type dropdown options -->
                          <ng-container *ngIf="item.type === 'break'">
                            <button
                              class="dropdown-item"
                              (click)="
                                onEditEventTime(
                                  item,
                                  getConfig(
                                    responseMetadata['locations'][locationKey]
                                      .id
                                  )?.id
                                )
                              "
                            >
                              <i class="fa fa-clock mr-2"></i>
                              {{ 'Edit Event Time' | translate }}
                            </button>
                            <button
                              class="dropdown-item"
                              (click)="
                                onUnscheduleTimeSlot(
                                  item,
                                  getConfig(
                                    responseMetadata['locations'][locationKey]
                                      .id
                                  )?.id
                                )
                              "
                            >
                              <i class="fa fa-trash mr-2"></i>
                              {{ 'Delete Event' | translate }}
                            </button>
                          </ng-container>
                        </div>
                      </div>
                    </div>
                  </div>
                  <footer class="location-footer" *ngIf="!isLock">
                    <button
                      class="btn btn-link w-100"
                      (click)="
                        openModalAddBreak(
                          responseMetadata['locations'][locationKey].id,
                          listMatches[locationKey][
                            listMatches[locationKey].length - 1
                          ]?.time_slot_id,
                          getConfig(
                            responseMetadata['locations'][locationKey].id
                          )?.id
                        )
                      "
                    >
                      <i class="fa fa-plus" aria-hidden="true"></i>
                      Add event / break
                    </button>
                  </footer>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
        <ng-container *ngIf="!hasPlan && !isFetching && !isLock">
          <ng-container *ngTemplateOutlet="noSchedule"></ng-container>
        </ng-container>
        <ng-container *ngIf="isFetching">
          <ng-container *ngTemplateOutlet="fetchingState"></ng-container>
        </ng-container>
      </div>
      <div id="listUnScheduleZone" class="col-3">
        <div class="unschedule-container">
          <div class="location-column unplanned-matches-container">
            <div class="location-header">
              <p class="h4">Not Planned</p>
              <p class="small">
                You can add unscheduled matches to the calendar by drag and drop
                them.
              </p>
            </div>
            <div
              class="dnd-zone"
              cdkDropList
              [cdkDropListData]="listUnScheduledMatches"
              (cdkDropListDropped)="drop($event)"
              id="unScheduleZone"
            >
              <ng-container *ngIf="hasMatches">
                <div
                  *ngFor="let item of listUnScheduledMatches"
                  class="dnd-item location-match-row"
                  cdkDrag
                  [data_stage_id]="item.stage_id"
                  [data_time_slot_id]="item.time_slot_id"
                  [data_type]="item.type"
                  [cdkDragDisabled]="isLock"
                >
                  <ng-container *ngIf="item.type === 'match'">
                    <ng-container
                      *ngTemplateOutlet="
                        matchNotScheduledTemplate;
                        context: { $implicit: item }
                      "
                    ></ng-container>
                  </ng-container>
                </div>
              </ng-container>
              <ng-container *ngIf="!hasMatches">
                <div id="notHaveMatches">
                  <p class="text-center">
                    No matches found for this tournament.
                  </p>

                  <button (click)="autoGenerate()" class="btn btn-primary">
                    <i class="fa-solid fa-wand-magic-sparkles"></i>
                    Auto Generate
                  </button>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #modalSetupSchedule let-modal>
  <app-modal-setup-schedule
    [seasonId]="seasonId"
    [tournamentId]="tournamentId"
    [tournamentInfo]="tournamentInfo"
    (onSubmit)="onScheduleAction($event)"
  />
</ng-template>
<ng-template #modalEditSchedule let-modal>
  <app-modal-update-config
    [selectedConfig]="selectedConfig"
    (onSubmit)="onScheduleAction($event)"
  />
</ng-template>
<ng-template #modalCrudBreak let-modal>
  <app-modal-crud-break
    [breakModalParams]="breakModalParams"
    (onSubmit)="onScheduleAction($event)"
  />
</ng-template>

<ng-template #modalUpdateMatch let-modal>
  <app-modal-update-match
    [timeSlotInfo]="selectedItem"
    [seasonId]="seasonId"
    (onSubmit)="onScheduleAction($event)"
  />
</ng-template>

<ng-template #noSchedule>
  <div id="noSchedule">
    <div
      class="col d-flex flex-column align-items-center justify-content-center g-2"
      style="height: 500px"
    >
      <p class="h5">No Plan Created</p>
      <p style="color: rgba(168, 170, 174, 1)" class="w-75 text-center">
        Please enter the necessary information to allow the system to generate
        an accurate schedule based on your requirements.
      </p>
      <button class="btn btn-primary" (click)="openModalSetupSchedule()">
        Setup
      </button>
    </div>
  </div>
</ng-template>

<ng-template #fetchingState>
  <div id="fetchingState">
    <div
      class="col d-flex flex-column align-items-center justify-content-center g-2"
      style="height: 500px"
    >
      <p class="h5">Fetching</p>
      <p style="color: rgba(168, 170, 174, 1)" class="w-75 text-center">
        Waiting for getting schedule data...
      </p>
    </div>
  </div>
</ng-template>

<ng-template #matchNotScheduledTemplate let-item>
  <div class="match-info-header">
    <p class="text-center m-0">{{ item.match.round_name }}</p>
  </div>
  <div class="team-row">
    <div class="home-team">
      <img
        src="https://assets.codepen.io/285131/whufc.svg"
        alt="Home Team Logo"
        class="team-logo"
      />
      <span class="h6 team-name">{{
        item.match?.home_team?.name ?? 'TBD'
      }}</span>
    </div>
    <button class="swap-button" (click)="swapTeam(item.match)">
      <i class="fa fa-right-left" aria-hidden="true"></i>
    </button>
    <div class="away-team">
      <span class="h6 team-name">{{
        item.match?.away_team?.name ?? 'TBD'
      }}</span>
      <img
        src="https://assets.codepen.io/285131/whufc.svg"
        alt="Away Team Logo"
        class="team-logo"
      />
    </div>
  </div>
</ng-template>

<ng-template #matchScheduledTemplate let-item>
  <div class="match-info-header">
    <p class="text-center m-0">
      {{ item.match.round_name }}
    </p>
  </div>
  <div class="team-row">
    <div class="home-team">
      <img
        src="https://assets.codepen.io/285131/whufc.svg"
        alt="Home Team Logo"
        class="team-logo"
      />
      <span
        class="h6 team-name"
        [ngClass]="{
          'text-danger': isMatchHasConflict(
            item.id,
            'team',
            item.match.home_team_id
          )
        }"
      >
        {{ item.match.home_team?.name ?? 'TBD' }}
      </span>
    </div>
    <button class="swap-button" (click)="swapTeam(item.match)">
      <i class="fa fa-right-left" aria-hidden="true"></i>
    </button>
    <div class="away-team">
      <span
        class="h6 team-name"
        [ngClass]="{
          'text-danger': isMatchHasConflict(
            item.id,
            'team',
            item.match.away_team_id
          )
        }"
      >
        {{ item.match.away_team?.name ?? 'TBD' }}
      </span>
      <img
        src="https://assets.codepen.io/285131/whufc.svg"
        alt="Away Team Logo"
        class="team-logo"
      />
    </div>
  </div>
  <div class="match-date">
    <i class="fa-regular fa-clock" aria-hidden="true"></i>
    <p class="text-center m-0">
      {{ getShortTime(item.start_time) }}
    </p>
    <p class="text-center m-0">-</p>
    <p class="text-center m-0">
      {{ getShortTime(item.end_time) }}
    </p>
  </div>
  <div class="referees-row" *ngIf="item.referees && item.referees.length > 0">
    <i class="fa-regular fa-whistle" style="rotate: -45deg"></i>

    <div
      class="referee-names"
      *ngFor="let ref of item.referees; let last = last"
    >
      <div class="referee-name">
        <span
          *ngIf="ref.referee_type == 'user'"
          [ngClass]="{
            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)
          }"
        >
          {{ ref.user?.first_name }} {{ ref.user?.last_name }}
        </span>
        <span
          *ngIf="ref.referee_type == 'freetext'"
          [ngClass]="{
            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)
          }"
        >
          {{ ref.referee_name }}
        </span>
        <span *ngIf="!last">-</span>
      </div>
    </div>
  </div>
</ng-template>
