<?php

namespace App\DataTables;

use App\Models\Location;
use App\Rules\LatitudeRule;
use App\Rules\LongitudeRule;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\QueryException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\DataTablesEditor;
use Exception;

class LocationsDataTableEditor extends DataTablesEditor
{
    protected $model = Location::class;

    /**
     * Get create action validation rules.
     */
    public function createRules(): array
    {
        return [
            'name' => 'required|max:100|unique:' . $this->resolveModel()->getTable(),
            'address' => 'required|max:191',
            'latitude' => ['nullable', 'numeric', new LatitudeRule],
            'longitude' => ['nullable', 'numeric', new LongitudeRule],
            'surface' => 'max:100',
            'parking' => 'max:100',
        ];
    }

    /**
     * Get edit action validation rules.
     */
    public function editRules(Model $model): array
    {
        return [
            'name' => 'required|max:100|' . Rule::unique($model->getTable())->ignore($model->getKey()),
            'address' => 'required|max:191',
            'latitude' => ['nullable', 'numeric', new LatitudeRule],
            'longitude' => ['nullable', 'numeric', new LongitudeRule],
            'surface' => 'max:100',
            'parking' => 'max:100',
        ];
    }

    /**
     * Get remove action validation rules.
     */
    public function removeRules(Model $model): array
    {
        return [];
    }

    /**
     * Event hook that is fired before the model is deleted.
     */
    public function deleting(Model $model, array $data)
    {
        try {
            // Check if location is being used by other models
            // This is a preventive check before attempting deletion
            Log::info('$this->isLocationInUse($model)', [$this->isLocationInUse($model)]);
            if ($this->isLocationInUse($model)) {
                throw new Exception('Cannot delete location because it is currently in use by the system.');
            }
        } catch (QueryException $e) {
            // Handle MySQL foreign key constraint error
            if ($e->getCode() === '23000') {
                throw new Exception('Cannot delete location because it is currently in use by the system.');
            }

            throw new Exception($e->getMessage());
        }

        return $data;
    }

    /**
     * Event hook that is fired after the model is deleted.
     */
    public function deleted(Model $model, array $data): array
    {
        return $data;
    }

    /**
     * Check if location is currently in use by other models
     */
    private function isLocationInUse(Model $location): bool
    {
        // Add your specific checks here based on your database relationships
        // Example checks (adjust based on your actual relationships):

        // Check if location has any matches
        if ($location->matches()->exists()) {
            return true;
        }

        // Check if location has any schedule time slots
        if ($location->scheduleTimeSlots()->exists()) {
            return true;
        }

        // Add more relationship checks as needed

        return false;
    }

    /**
     * Event hook that is fired after `creating` and `updating` hooks, but before
     * the model is saved to the database.
     */
    public function saving(Model $model, array $data): array
    {
        // set default values
        if (!isset($data['latitude'])) {
            $data['latitude'] = null;
        }
        if (!isset($data['longitude'])) {
            $data['longitude'] = null;
        }
        if (!isset($data['surface'])) {
            $data['surface'] = null;
        }
        if (!isset($data['parking'])) {
            $data['parking'] = null;
        }

        return $data;
    }

    /**
     * Event hook that is fired after `created` and `updated` events.
     */
    public function saved(Model $model, array $data): Model
    {
        // do something after saving the model

        return $model;
    }
}
