{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport Swal from 'sweetalert2';\nimport { AppConfig } from \"../../../app-config\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/tournament.service\";\nimport * as i3 from \"../../../services/stage.service\";\nimport * as i4 from \"../../../services/loading.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"../../../services/auto-schedule.service\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"ngx-toastr\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i14 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i15 from \"app/layout/components/content-header/content-header.component\";\nimport * as i16 from \"@ng-select/ng-select\";\nimport * as i17 from \"@angular/cdk/drag-drop\";\nimport * as i18 from \"./modal-setup-schedule/modal-setup-schedule.component\";\nimport * as i19 from \"./modal-update-config/modal-update-config.component\";\nimport * as i20 from \"./modal-crud-break/modal-crud-break.component\";\nimport * as i21 from \"./modal-update-match/modal-update-match.component\";\nconst _c0 = [\"modalSetupSchedule\"];\nconst _c1 = [\"modalEditSchedule\"];\nconst _c2 = [\"modalCrudBreak\"];\nconst _c3 = [\"modalUpdateMatch\"];\nfunction AutoScheduleComponent_ng_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", date_r22);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", date_r22, \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"button\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51)(4, \"a\", 52);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.editPlan(ctx_r28.responseMetadata[\"locations\"][locationKey_r24].id, ctx_r28.getConfigId(ctx_r28.responseMetadata[\"locations\"][locationKey_r24].id)));\n    });\n    i0.ɵɵtext(5, \" Edit Plan \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 52);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.deletePlan(locationKey_r24));\n    });\n    i0.ɵɵtext(7, \" Delete Plan \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 59);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(3);\n    const _r20 = i0.ɵɵreference(51);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r20)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, item_r33));\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 60)(2, \"p\", 61);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 62);\n    i0.ɵɵelement(5, \"i\", 63);\n    i0.ɵɵelementStart(6, \"p\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r33.description || \"Break time\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r33.break_durations, \" mins \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const item_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r42.onEditMatch(item_r33));\n    });\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const item_r33 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.onUnscheduleTimeSlot(item_r33, ctx_r45.getConfigId(ctx_r45.responseMetadata[\"locations\"][locationKey_r24].id)));\n    });\n    i0.ɵɵelement(6, \"i\", 67);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Update Match Referees\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Unschedule Match\"), \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const item_r33 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.onEditEventTime(item_r33, ctx_r48.getConfigId(ctx_r48.responseMetadata[\"locations\"][locationKey_r24].id)));\n    });\n    i0.ɵɵelement(2, \"i\", 68);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const item_r33 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.onUnscheduleTimeSlot(item_r33, ctx_r52.getConfigId(ctx_r52.responseMetadata[\"locations\"][locationKey_r24].id)));\n    });\n    i0.ɵɵelement(6, \"i\", 67);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Edit Event Time\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Delete Event\"), \" \");\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    \"conflict-border\": a0\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_10_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const item_r33 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r55.isLock && ctx_r55.toggleDropdown($event, item_r33));\n    });\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_10_button_1_Template, 2, 0, \"button\", 54);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_2_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_3_Template, 8, 2, \"ng-container\", 19);\n    i0.ɵɵelementStart(4, \"div\", 55)(5, \"div\", 56);\n    i0.ɵɵtemplate(6, AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_6_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_10_ng_container_7_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r33 = ctx.$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c5, ctx_r26.isMatchHasConflict(item_r33.id, \"match\")))(\"data_stage_id\", item_r33.stage_id)(\"data_time_slot_id\", item_r33.time_slot_id)(\"data_type\", item_r33.type)(\"cdkDragDisabled\", ctx_r26.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.isMatchHasConflict(item_r33.id, \"match\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r33.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r33.type === \"break\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"visible\", ctx_r26.isDropdownOpen(item_r33));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r33.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r33.type === \"break\");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_footer_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 69)(1, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_footer_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const locationKey_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.openModalAddBreak(ctx_r57.responseMetadata[\"locations\"][locationKey_r24].id, ctx_r57.listMatches[locationKey_r24][ctx_r57.listMatches[locationKey_r24].length - 1] == null ? null : ctx_r57.listMatches[locationKey_r24][ctx_r57.listMatches[locationKey_r24].length - 1].time_slot_id, ctx_r57.getConfigId(ctx_r57.responseMetadata[\"locations\"][locationKey_r24].id)));\n    });\n    i0.ɵɵelement(2, \"i\", 71);\n    i0.ɵɵtext(3, \" Add event / break \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"header\", 23)(4, \"div\", 41)(5, \"p\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_7_Template, 8, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 45);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_div_22_ng_container_2_Template_div_cdkDropListDropped_9_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.drop($event));\n    });\n    i0.ɵɵtemplate(10, AutoScheduleComponent_div_22_ng_container_2_div_10_Template, 8, 14, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, AutoScheduleComponent_div_22_ng_container_2_footer_11_Template, 4, 0, \"footer\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const locationKey_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", locationKey_r24, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.isLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r23.listMatches[locationKey_r24])(\"id\", locationKey_r24)(\"data_location_id\", ctx_r23.responseMetadata[\"locations\"][locationKey_r24].id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.listMatches[locationKey_r24]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.isLock);\n  }\n}\nfunction AutoScheduleComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_Template, 12, 7, \"ng-container\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listLocationIds);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_23_ng_container_1_Template, 1, 0, \"ng-container\", 72);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r14 = i0.ɵɵreference(45);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r14);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_24_ng_container_1_Template, 1, 0, \"ng-container\", 72);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r16 = i0.ɵɵreference(47);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r16);\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 59);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r65 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(2);\n    const _r18 = i0.ɵɵreference(49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r18)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, item_r65));\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_ng_container_1_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r65 = ctx.$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data_stage_id\", item_r65.stage_id)(\"data_time_slot_id\", item_r65.time_slot_id)(\"data_type\", item_r65.type)(\"cdkDragDisabled\", ctx_r64.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r65.type === \"match\");\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_Template, 2, 5, \"div\", 73);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listUnScheduledMatches);\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 75)(2, \"p\", 76);\n    i0.ɵɵtext(3, \" No matches found for this tournament. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_container_35_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.autoGenerate());\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵtext(6, \" Auto Generate \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-setup-schedule\", 79);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_36_Template_app_modal_setup_schedule_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r7.seasonId)(\"tournamentId\", ctx_r7.tournamentId)(\"tournamentInfo\", ctx_r7.tournamentInfo);\n  }\n}\nfunction AutoScheduleComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-config\", 80);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_38_Template_app_modal_update_config_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedConfig\", ctx_r9.selectedConfig);\n  }\n}\nfunction AutoScheduleComponent_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-crud-break\", 81);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_40_Template_app_modal_crud_break_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"breakModalParams\", ctx_r11.breakModalParams);\n  }\n}\nfunction AutoScheduleComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-match\", 82);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_42_Template_app_modal_update_match_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"timeSlotInfo\", ctx_r13.selectedItem)(\"seasonId\", ctx_r13.seasonId);\n  }\n}\nfunction AutoScheduleComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"p\", 85);\n    i0.ɵɵtext(3, \"No Plan Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 86);\n    i0.ɵɵtext(5, \" Please enter the necessary information to allow the system to generate an accurate schedule based on your requirements. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_44_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.openModalSetupSchedule());\n    });\n    i0.ɵɵtext(7, \" Setup \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 84)(2, \"p\", 85);\n    i0.ɵɵtext(3, \"Fetching\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 86);\n    i0.ɵɵtext(5, \" Waiting for getting schedule data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"p\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 89)(4, \"div\", 90);\n    i0.ɵɵelement(5, \"img\", 91);\n    i0.ɵɵelementStart(6, \"span\", 92);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_48_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r87);\n      const item_r85 = restoredCtx.$implicit;\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.swapTeam(item_r85.match));\n    });\n    i0.ɵɵelement(9, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 95)(11, \"span\", 92);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r85 = ctx.$implicit;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r85.match.round_name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_1_0 = item_r85.match == null ? null : item_r85.match.home_team == null ? null : item_r85.match.home_team.name) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"TBD\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_2_0 = item_r85.match == null ? null : item_r85.match.away_team == null ? null : item_r85.match.away_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\");\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"text-danger\": a0\n  };\n};\nfunction AutoScheduleComponent_ng_template_50_div_22_div_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r91 = i0.ɵɵnextContext().$implicit;\n    const item_r88 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r93 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c6, ctx_r93.isMatchHasConflict(item_r88.id, \"referee\", ref_r91.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ref_r91.user == null ? null : ref_r91.user.first_name, \" \", ref_r91.user == null ? null : ref_r91.user.last_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_div_22_div_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r91 = i0.ɵɵnextContext().$implicit;\n    const item_r88 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r94 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c6, ctx_r94.isMatchHasConflict(item_r88.id, \"referee\", ref_r91.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ref_r91.referee_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_div_22_div_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_50_div_22_div_2_span_2_Template, 2, 5, \"span\", 105);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_50_div_22_div_2_span_3_Template, 2, 4, \"span\", 105);\n    i0.ɵɵtemplate(4, AutoScheduleComponent_ng_template_50_div_22_div_2_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ref_r91 = ctx.$implicit;\n    const last_r92 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ref_r91.referee_type == \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ref_r91.referee_type == \"freetext\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r92);\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"i\", 101);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_50_div_22_div_2_Template, 5, 3, \"div\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r88 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r88.referees);\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"p\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 89)(4, \"div\", 90);\n    i0.ɵɵelement(5, \"img\", 91);\n    i0.ɵɵelementStart(6, \"span\", 97);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_50_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r102);\n      const item_r88 = restoredCtx.$implicit;\n      const ctx_r101 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r101.swapTeam(item_r88.match));\n    });\n    i0.ɵɵelement(9, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 95)(11, \"span\", 97);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 98);\n    i0.ɵɵelement(15, \"i\", 63);\n    i0.ɵɵelementStart(16, \"p\", 61);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 61);\n    i0.ɵɵtext(19, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 61);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AutoScheduleComponent_ng_template_50_div_22_Template, 3, 1, \"div\", 99);\n  }\n  if (rf & 2) {\n    const item_r88 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r88.match.round_name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c6, ctx_r21.isMatchHasConflict(item_r88.id, \"team\", item_r88.match.home_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = item_r88.match.home_team == null ? null : item_r88.match.home_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c6, ctx_r21.isMatchHasConflict(item_r88.id, \"team\", item_r88.match.away_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_4_0 = item_r88.match.away_team == null ? null : item_r88.match.away_team.name) !== null && tmp_4_0 !== undefined ? tmp_4_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getShortTime(item_r88.start_time), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getShortTime(item_r88.end_time), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r88.referees && item_r88.referees.length > 0);\n  }\n}\nconst _c7 = function (a0, a1) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-primary\": a1\n  };\n};\nexport class AutoScheduleComponent {\n  constructor(_route, _router, _tournamentService, _stageService, _loadingService, _titleService, _translateService, _autoScheduleService, _modalService, _toastService) {\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this._titleService = _titleService;\n    this._translateService = _translateService;\n    this._autoScheduleService = _autoScheduleService;\n    this._modalService = _modalService;\n    this._toastService = _toastService;\n    this.leagueOrGroupStageId = null;\n    this.dateOptions = [];\n    this.listLocations = {};\n    this.listMatches = {};\n    this.listLocationIds = [];\n    this.responseMetadata = {};\n    this.responseMatches = {};\n    this.teamConflicts = {};\n    this.refereeConflicts = {};\n    this.stageConflicts = {};\n    this.selectedConfig = null;\n    this.isFetching = true;\n    this.isLock = false;\n    this.hasMatches = false;\n    // public listUnScheduledMatches = [\n    // ];\n    this.listUnScheduledMatches = [];\n    this.selectedDate = null;\n    this.hasPlan = false;\n    // Simple dropdown state - track which dropdown is open\n    this.activeDropdownId = null;\n    this.breakModalParams = {\n      locationId: null,\n      tournamentId: null,\n      timeSlotId: null,\n      lastTimeSlotId: null,\n      configId: null\n    };\n    this.selectedItem = null;\n    this.location = location;\n    this.isFetching = true;\n    this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\n    this._tournamentService.getTournament(this.tournamentId).subscribe(res => {\n      this.tournamentInfo = res;\n      this.leagueOrGroupStageId = res.stages.find(stage => {\n        if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\n        } else {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.league;\n        }\n      })?.id;\n      this.isLock = res.is_locked_schedule === 1;\n      this.seasonId = res.group.season.id;\n      _titleService.setTitle(res.name);\n      this.contentHeader = {\n        headerTitle: res.name,\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: this._translateService.instant('Leagues'),\n            isLink: false\n          }, {\n            name: this._translateService.instant('Manage Leagues'),\n            isLink: true,\n            link: '/leagues/manage'\n          }, {\n            name: res.name,\n            isLink: false\n          }, {\n            name: this._translateService.instant('Auto Schedule'),\n            isLink: false\n          }]\n        }\n      };\n      this.onScheduleAction(null, true);\n    });\n  }\n  ngOnInit() {}\n  ngAfterViewChecked() {\n    setTimeout(() => {\n      this.hasMatches = this.responseMatches && Object.keys(this.responseMatches).length > 0 || this.listUnScheduledMatches.length > 0;\n    }, 0);\n  }\n  getScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    this._autoScheduleService.getScheduleMatches(this.tournamentId).subscribe(res => {\n      this.responseMetadata = res.metadata;\n      this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\n      this.teamConflicts = res.conflicts['team_scheduling_conflict'];\n      this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\n      this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\n      if (this.responseMatches) {\n        this.dateOptions = Object.keys(this.responseMatches);\n        this.selectedDate = this.dateOptions[0];\n        this.mapListLocations();\n      } else {\n        this.listLocationIds = [];\n        this.listMatches = {};\n        this.dateOptions = [];\n        this.selectedDate = null;\n      }\n      this._loadingService.dismiss();\n    }, () => {}, () => {\n      this.isFetching = false;\n      this.hasPlan = this.dateOptions.length > 0;\n    });\n  }\n  getUnScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    this._autoScheduleService.getListUnScheduledMatches(this.tournamentId).subscribe(res => {\n      this.listUnScheduledMatches = res.data;\n      this._loadingService.dismiss();\n    });\n  }\n  mapListLocations() {\n    this.listLocationIds = [];\n    this.listMatches = {};\n    this.listLocations = this.responseMatches[this.selectedDate] || {};\n    if (!this.listLocations || !this.selectedDate) return;\n    Object.keys(this.listLocations).forEach(locationName => {\n      if (!this.listLocationIds.includes(`${locationName}`)) {\n        this.listLocationIds.push(`${locationName}`);\n      }\n      if (!this.listMatches[locationName]) {\n        this.listMatches[locationName] = [];\n      }\n      this.listMatches[locationName] = [...this.listMatches[locationName], ...this.listLocations[locationName]];\n    });\n  }\n  onSelectDate(event) {\n    this.selectedDate = event;\n    this.mapListLocations();\n  }\n  openModalSetupSchedule() {\n    this._modalService.open(this.modalSetupSchedule, {\n      centered: true,\n      size: 'lg'\n    });\n  }\n  onScheduleAction(response, showLoading = true) {\n    this.getScheduleMatches(showLoading);\n    this.getUnScheduleMatches(showLoading);\n  }\n  unScheduleMatch(timeSlotId, configId, onError, onSuccess) {\n    this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe(res => {\n      this.onScheduleAction(null, false);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  mapNewSlotIndex(locationKey) {\n    const newSlotIndex = {};\n    this.listMatches[locationKey].forEach((item, index) => {\n      newSlotIndex[item.time_slot_id] = index;\n    });\n    return newSlotIndex;\n  }\n  updateLocationMatch(locationKey, updateData, onError, onSuccess) {\n    const newIndex = this.mapNewSlotIndex(locationKey);\n    this._autoScheduleService.updateLocationMatch(updateData).subscribe(res => {\n      this.onScheduleAction(null, false);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  drop(event) {\n    const targetContainer = event.container.element.nativeElement;\n    const prevContainer = event.previousContainer.element.nativeElement;\n    const dragItem = event.item.element.nativeElement;\n    const targetContainerId = targetContainer['id'];\n    const prevContainerId = prevContainer['id'];\n    const targetStageId = targetContainer['data_stage_id'];\n    const targetLocationId = targetContainer['data_location_id'];\n    const prevStageId = prevContainer['data_stage_id'];\n    const prevLocationId = prevContainer['data_location_id'];\n    const itemStageId = dragItem['data_stage_id'];\n    const itemTimeSlotId = dragItem['data_time_slot_id'];\n    const itemType = dragItem['data_type'];\n    if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\n    if (targetContainerId === 'unScheduleZone') {\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      if (targetContainerId === prevContainerId) return;\n      this.unScheduleMatch(itemTimeSlotId, this.getConfigId(prevLocationId), () => {\n        transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n        this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\n      }, () => {\n        this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\n      });\n    } else {\n      if (event.previousContainer === event.container) {\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n        this.updateLocationMatch(targetContainerId, {\n          new_index: this.mapNewSlotIndex(targetContainerId),\n          location_id: targetLocationId,\n          prev_location_id: prevLocationId,\n          stage_id: targetStageId,\n          prev_stage_id: prevStageId,\n          tournament_id: this.tournamentId,\n          config_id: this.getConfigId(targetLocationId),\n          prev_config_id: this.getConfigId(prevLocationId)\n        }, () => {\n          moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\n          this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n        }, () => {\n          this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n        });\n      } else {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n        this.updateLocationMatch(targetContainerId, {\n          new_index: this.mapNewSlotIndex(targetContainerId),\n          location_id: targetLocationId,\n          prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\n          stage_id: targetStageId,\n          prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\n          tournament_id: this.tournamentId,\n          config_id: this.getConfigId(targetLocationId),\n          prev_config_id: this.getConfigId(prevLocationId)\n        }, () => {\n          transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n          this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n        }, () => {\n          this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n        });\n      }\n    }\n  }\n  getShortTime(time) {\n    // handle return ISO string to short time format HH:mm and format 24 hours\n    if (!time) return '';\n    const date = new Date(time);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n  getConfigId(location_id) {\n    return this.responseMetadata['configs'].find(item => {\n      return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\n    })?.id;\n  }\n  editPlan(locationId, configId) {\n    this.selectedConfig = {\n      tournamentId: this.tournamentId,\n      location: locationId,\n      date: this.selectedDate,\n      configId\n    };\n    this._modalService.open(this.modalEditSchedule, {\n      centered: true\n    });\n  }\n  deletePlan(locationKey) {\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const configId = this.getConfigId(locationId);\n    const timeSlotIds = this.listMatches[locationKey].map(item => item.time_slot_id);\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('You will not be able to recover this schedule!'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe(res => {\n          this.onScheduleAction(null, false);\n          this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\n        });\n      }\n    });\n  }\n  openModalAddBreak(locationId, lastTimeSlotId, configId) {\n    this.breakModalParams = {\n      ...this.breakModalParams,\n      locationId,\n      tournamentId: this.tournamentId,\n      lastTimeSlotId,\n      configId\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  // Simple dropdown methods\n  toggleDropdown(event, item) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.target['className'] === 'swap-button') {\n      return;\n    }\n    const dropdownId = this.getDropdownId(item);\n    // Close dropdown if clicking on the same item\n    if (this.activeDropdownId === dropdownId) {\n      this.activeDropdownId = null;\n    } else {\n      // Open new dropdown (close any existing one)\n      this.activeDropdownId = dropdownId;\n    }\n  }\n  isDropdownOpen(item) {\n    const dropdownId = this.getDropdownId(item);\n    return this.activeDropdownId === dropdownId;\n  }\n  getDropdownId(item) {\n    // Create unique ID for each item\n    return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\n  }\n  closeAllDropdowns() {\n    this.activeDropdownId = null;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    // Close dropdown if clicking outside of dropdown or dnd-item\n    if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\n      this.closeAllDropdowns();\n    }\n  }\n  // Dropdown action handlers\n  onEditMatch(item) {\n    this.closeAllDropdowns();\n    this.selectedItem = item;\n    this._modalService.open(this.modalUpdateMatch, {\n      centered: true\n    });\n  }\n  onUnscheduleTimeSlot(item, configId) {\n    const description = {\n      match: 'This match will be moved to unscheduled matches.',\n      break: 'This break will be removed.'\n    };\n    const successMessage = {\n      match: 'Match unscheduled successfully.',\n      break: 'Break removed successfully.'\n    };\n    const errorMessage = {\n      match: 'Failed to unschedule match.',\n      break: 'Failed to remove break.'\n    };\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant(description[item.type]),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.unScheduleMatch(item.time_slot_id, configId, () => {\n          this._toastService.error(this._translateService.instant(errorMessage[item.type]));\n        }, () => {\n          this._toastService.success(this._translateService.instant(successMessage[item.type]));\n        });\n      }\n    });\n    this.closeAllDropdowns();\n  }\n  onEditEventTime(item, configId) {\n    this.closeAllDropdowns();\n    this.breakModalParams = {\n      timeSlotId: item.time_slot_id,\n      locationId: item.location_id,\n      tournamentId: item.tournament_id,\n      description: item.description,\n      breakDurations: item.break_durations,\n      configId: configId\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  clearSchedule() {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('This action will clear all schedule.'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes, clear it!'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._autoScheduleService.clearSchedule(this.tournamentId).subscribe(res => {\n          Swal.fire({\n            title: this._translateService.instant('Success!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n          this.onScheduleAction(null, false);\n        }, error => {\n          Swal.fire({\n            title: this._translateService.instant('Error!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n        });\n      }\n    });\n  }\n  isMatchHasConflict(scheduleMatchId, type, itemCheckId) {\n    switch (type) {\n      case 'team':\n        return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\n      case 'referee':\n        return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\n      case 'match':\n        return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\n    }\n  }\n  onClickLock() {\n    this.isLock = !this.isLock;\n    this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe(res => {\n      this._toastService.success(res.message);\n    }, error => {\n      this.isLock = !this.isLock;\n      this._toastService.error(error.message);\n    });\n  }\n  swapTeam(matchInfo) {\n    this.closeAllDropdowns();\n    this._stageService.swapTeams(matchInfo).subscribe(res => {\n      this._toastService.success('Swap teams successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      this._toastService.error(error.message || 'Failed to swap teams.');\n    });\n  }\n  autoGenerate() {\n    this._stageService.autoGenerateMatches(this.leagueOrGroupStageId).subscribe(res => {\n      this._toastService.success('Auto generate matches successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      this._toastService.error(error.warning || error.message || 'Failed to auto generate matches.');\n    });\n  }\n  static #_ = this.ɵfac = function AutoScheduleComponent_Factory(t) {\n    return new (t || AutoScheduleComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TournamentService), i0.ɵɵdirectiveInject(i3.StageService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.Title), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.AutoScheduleService), i0.ɵɵdirectiveInject(i8.NgbModal), i0.ɵɵdirectiveInject(i9.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AutoScheduleComponent,\n    selectors: [[\"app-auto-schedule\"]],\n    viewQuery: function AutoScheduleComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalSetupSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalEditSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalCrudBreak = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalUpdateMatch = _t.first);\n      }\n    },\n    hostBindings: function AutoScheduleComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 52,\n    vars: 24,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"top-header\"], [1, \"row\", \"mb-1\"], [1, \"d-flex\", \"flex-column\", 2, \"gap\", \"2px\"], [\"for\", \"selectDate\"], [\"id\", \"selectDate\", 2, \"min-width\", \"200px\", 3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 2, \"gap\", \"8px\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"data-feather\", \"settings\", 1, \"mr-25\"], [1, \"btn\", \"btn-icon\", 3, \"ngClass\", \"click\"], [3, \"ngClass\"], [1, \"btn\", \"btn-icon\", \"btn-outline-danger\", 3, \"disabled\", \"click\"], [\"data-feather\", \"trash-2\"], [\"cdkDropListGroup\", \"\"], [\"id\", \"listLocationZone\", 1, \"col-9\"], [\"class\", \"horizontal-scroll-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"listUnScheduleZone\", 1, \"col-3\"], [1, \"unschedule-container\"], [1, \"location-column\", \"unplanned-matches-container\"], [1, \"location-header\"], [1, \"h4\"], [1, \"small\"], [\"cdkDropList\", \"\", \"id\", \"unScheduleZone\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"cdkDropListDropped\"], [\"modalSetupSchedule\", \"\"], [\"modalEditSchedule\", \"\"], [\"modalCrudBreak\", \"\"], [\"modalUpdateMatch\", \"\"], [\"noSchedule\", \"\"], [\"fetchingState\", \"\"], [\"matchNotScheduledTemplate\", \"\"], [\"matchScheduledTemplate\", \"\"], [3, \"value\"], [1, \"horizontal-scroll-container\"], [1, \"location-columns-wrapper\"], [4, \"ngFor\", \"ngForOf\"], [1, \"location-column\", \"mb-2\"], [1, \"bg-white\", \"shadow-sm\"], [1, \"d-flex\", \"align-items-start\", \"justify-content-between\"], [1, \"location-name\", \"h4\"], [\"class\", \"\", \"ngbDropdown\", \"\", 4, \"ngIf\"], [1, \"location-stage-name\"], [\"cdkDropList\", \"\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"id\", \"data_location_id\", \"cdkDropListDropped\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"location-footer\", 4, \"ngIf\"], [\"ngbDropdown\", \"\", 1, \"\"], [\"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-link\", \"dropdown-toggle\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuButton\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", \"click\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"class\", \"conflict-tooltip btn btn-link\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 4, \"ngIf\"], [1, \"item-dropdown\"], [1, \"dropdown-content\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 1, \"conflict-tooltip\", \"btn\", \"btn-link\"], [1, \"fa-light\", \"fa-circle-exclamation\", 2, \"font-size\", \"16px\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"break-info-header\"], [1, \"text-center\", \"m-0\"], [1, \"break-row\"], [\"aria-hidden\", \"true\", 1, \"fa-regular\", \"fa-clock\"], [1, \"break-time\", \"m-0\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fa-regular\", \"fa-whistle\", \"mr-2\", 2, \"rotate\", \"-45deg\"], [1, \"fa\", \"fa-trash\", \"mr-2\"], [1, \"fa\", \"fa-clock\", \"mr-2\"], [1, \"location-footer\"], [1, \"btn\", \"btn-link\", \"w-100\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-plus\"], [4, \"ngTemplateOutlet\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\"], [\"id\", \"notHaveMatches\"], [1, \"text-center\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-wand-magic-sparkles\"], [3, \"seasonId\", \"tournamentId\", \"tournamentInfo\", \"onSubmit\"], [3, \"selectedConfig\", \"onSubmit\"], [3, \"breakModalParams\", \"onSubmit\"], [3, \"timeSlotInfo\", \"seasonId\", \"onSubmit\"], [\"id\", \"noSchedule\"], [1, \"col\", \"d-flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"g-2\", 2, \"height\", \"500px\"], [1, \"h5\"], [1, \"w-75\", \"text-center\", 2, \"color\", \"rgba(168, 170, 174, 1)\"], [\"id\", \"fetchingState\"], [1, \"match-info-header\"], [1, \"team-row\"], [1, \"home-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Home Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\"], [1, \"swap-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-right-left\"], [1, \"away-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Away Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\", 3, \"ngClass\"], [1, \"match-date\"], [\"class\", \"referees-row\", 4, \"ngIf\"], [1, \"referees-row\"], [1, \"fa-regular\", \"fa-whistle\", 2, \"rotate\", \"-45deg\"], [\"class\", \"referee-names\", 4, \"ngFor\", \"ngForOf\"], [1, \"referee-names\"], [1, \"referee-name\"], [3, \"ngClass\", 4, \"ngIf\"]],\n    template: function AutoScheduleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"label\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"ng-select\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function AutoScheduleComponent_Template_ng_select_ngModelChange_9_listener($event) {\n          return ctx.selectedDate = $event;\n        })(\"change\", function AutoScheduleComponent_Template_ng_select_change_9_listener($event) {\n          return ctx.onSelectDate($event);\n        });\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵtemplate(11, AutoScheduleComponent_ng_option_11_Template, 2, 2, \"ng-option\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_13_listener() {\n          return ctx.openModalSetupSchedule();\n        });\n        i0.ɵɵelement(14, \"i\", 11);\n        i0.ɵɵtext(15, \" Add Schedule \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_16_listener() {\n          return ctx.onClickLock();\n        });\n        i0.ɵɵelement(17, \"i\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_18_listener() {\n          return ctx.clearSchedule();\n        });\n        i0.ɵɵelement(19, \"i\", 15);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n        i0.ɵɵtemplate(22, AutoScheduleComponent_div_22_Template, 3, 1, \"div\", 18);\n        i0.ɵɵtemplate(23, AutoScheduleComponent_ng_container_23_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(24, AutoScheduleComponent_ng_container_24_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 20)(26, \"div\", 21)(27, \"div\", 22)(28, \"div\", 23)(29, \"p\", 24);\n        i0.ɵɵtext(30, \"Not Planned\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"p\", 25);\n        i0.ɵɵtext(32, \" You can add unscheduled matches to the calendar by drag and drop them. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 26);\n        i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_Template_div_cdkDropListDropped_33_listener($event) {\n          return ctx.drop($event);\n        });\n        i0.ɵɵtemplate(34, AutoScheduleComponent_ng_container_34_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(35, AutoScheduleComponent_ng_container_35_Template, 7, 0, \"ng-container\", 19);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵtemplate(36, AutoScheduleComponent_ng_template_36_Template, 1, 3, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(38, AutoScheduleComponent_ng_template_38_Template, 1, 1, \"ng-template\", null, 28, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(40, AutoScheduleComponent_ng_template_40_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(42, AutoScheduleComponent_ng_template_42_Template, 1, 2, \"ng-template\", null, 30, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(44, AutoScheduleComponent_ng_template_44_Template, 8, 0, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(46, AutoScheduleComponent_ng_template_46_Template, 6, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(48, AutoScheduleComponent_ng_template_48_Template, 14, 3, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(50, AutoScheduleComponent_ng_template_50_Template, 23, 12, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 17, \"Date\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(10, 19, \"Select Date\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.selectedDate);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dateOptions);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c7, !ctx.isLock, ctx.isLock));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.isLock ? \"fa-regular fa-lock\" : \"fa-regular fa-unlock\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasPlan);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasPlan && !ctx.isFetching);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isFetching);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"cdkDropListData\", ctx.listUnScheduledMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasMatches);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.DefaultClassDirective, i12.NgControlStatus, i12.NgModel, i13.RippleEffectDirective, i14.FeatherIconDirective, i8.NgbDropdown, i8.NgbDropdownToggle, i8.NgbDropdownMenu, i8.NgbDropdownItem, i8.NgbTooltip, i15.ContentHeaderComponent, i16.NgSelectComponent, i16.ɵr, i17.CdkDropList, i17.CdkDropListGroup, i17.CdkDrag, i18.ModalSetupScheduleComponent, i19.ModalUpdateConfigComponent, i20.ModalCrudBreakComponent, i21.ModalUpdateMatchComponent, i6.TranslatePipe],\n    styles: [\".home-team[_ngcontent-%COMP%], .away-team[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\nimg.team-logo[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n}\\n\\n.swap-button[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  background: rgba(45, 103, 241, 0.2);\\n  border-width: 0;\\n  padding: 0.25rem 1rem;\\n  border-radius: 0.5rem;\\n}\\n\\n.top-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 15;\\n  background-color: white;\\n  padding: 1rem;\\n  margin: 0;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  min-width: 100%;\\n}\\n.top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin: 0;\\n  width: 100%;\\n  min-width: 100%;\\n}\\n\\n.dnd-zone[_ngcontent-%COMP%] {\\n  background: #fff;\\n  min-height: 10rem;\\n}\\n\\n.dnd-item[_ngcontent-%COMP%] {\\n  -webkit-user-select: none;\\n          user-select: none;\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem 0.25rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 4px;\\n  height: 10rem;\\n  cursor: grab;\\n  position: relative;\\n  background: #fff;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .conflict-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  color: red;\\n}\\n.dnd-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.05);\\n}\\n.dnd-item[_ngcontent-%COMP%]:active {\\n  cursor: grabbing;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%] {\\n  z-index: 999;\\n  border-radius: 8px;\\n  border: 1px solid #a8aaae;\\n  scale: 0.95;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n.dnd-item.cdk-drag-preview.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n.dnd-item.location-match-row[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 100%;\\n  margin-left: -2rem;\\n  z-index: 1000;\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  border: 1px solid rgba(168, 170, 174, 0.25);\\n  min-width: 180px;\\n  overflow: hidden;\\n  display: none;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown.visible[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  color: rgb(75, 70, 92);\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  white-space: nowrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n  margin-right: 0.5rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  justify-content: center;\\n  align-items: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-names[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  align-items: center;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(16, 15, 15);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n  position: relative;\\n}\\n\\n.content-body[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n  width: 100%;\\n}\\n\\n#listLocationZone[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow-x: auto;\\n  padding: 0 !important;\\n  margin: 1rem 0;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n  scroll-behavior: smooth;\\n  width: 100%;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(45, 103, 241, 0.5);\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(45, 103, 241, 0.7);\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  min-width: max-content;\\n  width: max-content;\\n  position: relative;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  display: block;\\n  min-width: 28rem;\\n  flex-shrink: 0;\\n  height: 1px;\\n}\\n\\n#listUnScheduleZone[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: absolute;\\n  top: calc(10rem - 2px);\\n  right: 0;\\n  height: 100%;\\n  z-index: 10;\\n  margin: 1rem 0;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n  position: sticky !important;\\n  top: 8rem;\\n  height: max-content;\\n  z-index: 10;\\n  width: 100%;\\n  right: 0;\\n  margin: 1rem 0;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 1200px) {\\n  #listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 100%;\\n    right: 0;\\n    margin: 1rem 0;\\n  }\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   #unScheduleZone[_ngcontent-%COMP%] {\\n  z-index: 100;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%] {\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  height: max-content;\\n  max-height: calc(100vh - 15rem);\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container.location-column[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  border-bottom: 1px solid rgba(168, 170, 174, 0.25);\\n}\\n\\n.location-column[_ngcontent-%COMP%] {\\n  width: 26rem;\\n  height: max-content;\\n  overflow: visible;\\n  border: 1px solid #eee;\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  background: rgba(255, 0, 0, 0.1);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-name[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-stage-name[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column[_ngcontent-%COMP%]   .stage-conflict-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  bottom: 1rem;\\n  right: 0;\\n  color: red;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  padding: 1rem 0.5rem;\\n  background: rgba(45, 103, 241, 0.25);\\n  justify-content: space-between;\\n  height: 8rem;\\n  position: relative;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]::after {\\n  width: 0;\\n  background-image: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin: 0;\\n  width: 90%;\\n  color: rgb(45, 103, 241);\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-stage-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: rgb(45, 103, 241);\\n}\\n.location-column[_ngcontent-%COMP%]   .location-footer[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: stretch;\\n    padding-left: 0.5rem;\\n    padding-right: 0.5rem;\\n  }\\n  #listUnScheduleZone[_ngcontent-%COMP%] {\\n    position: relative !important;\\n    left: auto;\\n    right: auto;\\n    top: auto;\\n    width: 100%;\\n    max-width: 100%;\\n    margin-top: 2rem;\\n  }\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n  }\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    padding-left: 0.25rem;\\n    padding-right: 0.25rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n    left: 0;\\n    top: 100%;\\n    margin-left: 0;\\n    margin-top: 5px;\\n    min-width: 200px;\\n    max-width: 90vw;\\n  }\\n}\\n.horizontal-scroll-container[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n\\n.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.cdk-drag-disabled[_ngcontent-%COMP%] {\\n  background: rgba(220, 220, 220, 0.25);\\n  cursor: default;\\n}\\n.cdk-drag-disabled[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 220, 220, 0.25);\\n}\\n\\n#fetchingState[_ngcontent-%COMP%], #noSchedule[_ngcontent-%COMP%] {\\n  width: 75%;\\n}\\n\\n#notHaveMatches[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 10rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  z-index: 1000;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAAsBA,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;AAExF,OAAOC,IAAI,MAAM,aAAa;AAG9B,SAASC,SAAS,QAAQ,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICMnCC,qCACG;IAAAA,YACH;IAAAA,iBAAY;;;;IAFgCA,gCAAc;IACvDA,eACH;IADGA,wCACH;;;;;;IAkDUA,+BAA0C;IAKtCA,wBAA6C;IAC/CA,iBAAS;IACTA,+BAGC;IAGGA;MAAAA;MAAA;MAAA;MAAA,OAERA,yDAA2D,WACrE,uBACKC,6CAAgE,WACtE,sBACgB,CACD;IAAA,EAAK;IAEDD,2BACF;IAAAA,iBAAI;IACJA,6BAAqD;IAAlCA;MAAAA;MAAA;MAAA;MAAA,OAASA,kDAAuB;IAAA,EAAC;IAClDA,6BACF;IAAAA,iBAAI;;;;;IAmCRA,kCAQC;IACCA,wBAGK;IACPA,iBAAS;;;;;IAGPA,wBAKgB;;;;;;;;;;IANlBA,6BAA4C;IAC1CA,qIAKgB;IAClBA,0BAAe;;;;;;IALVA,eAE6B;IAF7BA,uCAE6B;;;;;IAIlCA,6BAA4C;IAC1CA,+BAA+B;IAE3BA,YACF;IAAAA,iBAAI;IAENA,+BAAuB;IACrBA,wBAAsD;IACtDA,6BAA0B;IACxBA,YACF;IAAAA,iBAAI;IAERA,0BAAe;;;;IATTA,eACF;IADEA,qEACF;IAKEA,eACF;IADEA,8DACF;;;;;;IAWAA,6BAA4C;IAC1CA,kCAGC;IADCA;MAAAA;MAAA;MAAA;MAAA,OAASA,4CAAiB;IAAA,EAAC;IAE3BA,wBAGK;IACLA,YACF;;IAAAA,iBAAS;IACTA,kCAWC;IATCA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAEpBA,sDAEOE,6CACd,WAAW,sBAEY,CACD;IAAA,EAAM;IAEDF,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACXA,0BAAe;;;IAjBXA,eACF;IADEA,8EACF;IAcEA,eACF;IADEA,yEACF;;;;;;IAIFA,6BAA4C;IAC1CA,kCAWC;IATCA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAEfA,iDAEEG,6CACd,WAAW,sBAEY,CACD;IAAA,EAAM;IAEDH,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACTA,kCAWC;IATCA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAEpBA,sDAEOI,6CACd,WAAW,sBAEY,CACD;IAAA,EAAM;IAEDJ,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACXA,0BAAe;;;IAjBXA,eACF;IADEA,wEACF;IAcEA,eACF;IADEA,qEACF;;;;;;;;;;;IApHRA,+BAYC;IADCA;MAAA;MAAA;MAAA;MAAA,OAASA,kCAAWK,wCAA4B;IAAA,EAAC;IAEjDL,0GAaS;IAETA,sHAOe;IACfA,sHAYe;IAGfA,+BAGC;IAGGA,sHA0Be;IAGfA,sHA+Be;IACjBA,iBAAM;;;;;IAnHRA,uGAEE;IAeCA,eAA0C;IAA1CA,uEAA0C;IAQ9BA,eAA2B;IAA3BA,gDAA2B;IAQ3BA,eAA2B;IAA3BA,gDAA2B;IAiBxCA,eAAsC;IAAtCA,2DAAsC;IAIrBA,eAA2B;IAA3BA,gDAA2B;IA6B3BA,eAA2B;IAA3BA,gDAA2B;;;;;;IAoClDA,kCAAgD;IAG5CA;MAAAA;MAAA;MAAA;MAAA,OAEjBA,kEAA8D,WAC/D,0GAEe,CAAC,sGAAD,CAAC,gBAEdM,6CAA0D,WAChE,sBACgB,CACD;IAAA,EAAQ;IAEDN,wBAA6C;IAC7CA,mCACF;IAAAA,iBAAS;;;;;;IArMjBA,6BAA0D;IACxDA,+BAAkC;IAOxBA,YACF;IAAAA,iBAAI;IACJA,6FA4BM;IACRA,iBAAM;IACNA,wBAMI;IACNA,iBAAS;IACTA,+BASC;IALCA;MAAAA;MAAA;MAAA,OAAsBA,mCAAY;IAAA,EAAC;IAMnCA,gGAwHM;IACRA,iBAAM;IACNA,qGAkBS;IACXA,iBAAM;IAEVA,0BAAe;;;;;IAjMHA,eACF;IADEA,gDACF;IAC2BA,eAAa;IAAbA,sCAAa;IAyC1CA,eAA4C;IAA5CA,sEAA4C;IAQzBA,eAA2B;IAA3BA,8DAA2B;IAyHfA,eAAa;IAAbA,sCAAa;;;;;IAtLxDA,+BAAyD;IAErDA,iGAyMe;IACjBA,iBAAM;;;;IA1MkCA,eAAkB;IAAlBA,gDAAkB;;;;;IA6M1DA,wBAA4D;;;;;IAD9DA,6BAA8C;IAC5CA,yGAA4D;IAC9DA,0BAAe;;;;;IADEA,eAA4B;IAA5BA,uCAA4B;;;;;IAG3CA,wBAA+D;;;;;IADjEA,6BAAiC;IAC/BA,yGAA+D;IACjEA,0BAAe;;;;;IADEA,eAA+B;IAA/BA,uCAA+B;;;;;IA+BpCA,wBAKgB;;;;;IANlBA,6BAA4C;IAC1CA,8HAKgB;IAClBA,0BAAe;;;;;;IALVA,eAEyB;IAFzBA,uCAEyB;;;;;IAbhCA,+BAQC;IACCA,+GAOe;IACjBA,iBAAM;;;;;IAbJA,iDAA+B;IAKhBA,eAA2B;IAA3BA,gDAA2B;;;;;IAV9CA,6BAAiC;IAC/BA,uFAiBM;IACRA,0BAAe;;;;IAjBMA,eAAyB;IAAzBA,uDAAyB;;;;;;IAkB9CA,6BAAkC;IAChCA,+BAAyB;IAErBA,uDACF;IAAAA,iBAAI;IAEJA,kCAAyD;IAAjDA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAC9BA,wBAA+C;IAC/CA,+BACF;IAAAA,iBAAS;IAEbA,0BAAe;;;;;;IAS3BA,oDAKE;IADAA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAJvCA,iBAKE;;;;IAJAA,0CAAqB;;;;;;IAOvBA,mDAGE;IADAA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAFvCA,iBAGE;;;;IAFAA,sDAAiC;;;;;;IAKnCA,gDAGE;IADAA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAFvCA,iBAGE;;;;IAFAA,2DAAqC;;;;;;IAMvCA,kDAIE;IADAA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAHvCA,iBAIE;;;;IAHAA,mDAA6B;;;;;;IAO/BA,+BAAqB;IAKHA,+BAAe;IAAAA,iBAAI;IACjCA,6BAAkE;IAChEA,yIAEF;IAAAA,iBAAI;IACJA,kCAAmE;IAAnCA;MAAAA;MAAA;MAAA,OAASA,+CAAwB;IAAA,EAAC;IAChEA,uBACF;IAAAA,iBAAS;;;;;IAMbA,+BAAwB;IAKNA,wBAAQ;IAAAA,iBAAI;IAC1BA,6BAAkE;IAChEA,sDACF;IAAAA,iBAAI;;;;;;IAMRA,+BAA+B;IACFA,YAA2B;IAAAA,iBAAI;IAE5DA,+BAAsB;IAElBA,0BAIE;IACFA,gCAA2B;IAAAA,YAEzB;IAAAA,iBAAO;IAEXA,kCAA2D;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACxDA,wBAAmD;IACrDA,iBAAS;IACTA,gCAAuB;IACMA,aAEzB;IAAAA,iBAAO;IACTA,2BAIE;IACJA,iBAAM;;;;;;IAzBqBA,eAA2B;IAA3BA,+CAA2B;IASzBA,eAEzB;IAFyBA,6LAEzB;IAMyBA,eAEzB;IAFyBA,6LAEzB;;;;;;;;;;IA6EAA,gCAKC;IACCA,YACF;IAAAA,iBAAO;;;;;;IALLA,oHAEE;IAEFA,eACF;IADEA,iJACF;;;;;IACAA,gCAKC;IACCA,YACF;IAAAA,iBAAO;;;;;;IALLA,oHAEE;IAEFA,eACF;IADEA,qDACF;;;;;IACAA,4BAAoB;IAAAA,iBAAC;IAAAA,iBAAO;;;;;IArBhCA,gCAGC;IAEGA,sGAOO;IACPA,sGAOO;IACPA,qGAA4B;IAC9BA,iBAAM;;;;;IAhBDA,eAAgC;IAAhCA,qDAAgC;IAQhCA,eAAoC;IAApCA,yDAAoC;IAOhCA,eAAW;IAAXA,gCAAW;;;;;IAxBxBA,gCAA4E;IAC1EA,yBAA4D;IAE5DA,8FAuBM;IACRA,iBAAM;;;;IAtBcA,eAAkB;IAAlBA,2CAAkB;;;;;;IA/DtCA,+BAA+B;IAE3BA,YACF;IAAAA,iBAAI;IAENA,+BAAsB;IAElBA,0BAIE;IACFA,gCASC;IACCA,YACF;IAAAA,iBAAO;IAETA,kCAA2D;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,gDAAoB;IAAA,EAAC;IACxDA,wBAAmD;IACrDA,iBAAS;IACTA,gCAAuB;IAWnBA,aACF;IAAAA,iBAAO;IACPA,2BAIE;IACJA,iBAAM;IAERA,gCAAwB;IACtBA,yBAAsD;IACtDA,8BAA2B;IACzBA,aACF;IAAAA,iBAAI;IACJA,8BAA2B;IAAAA,kBAAC;IAAAA,iBAAI;IAChCA,8BAA2B;IACzBA,aACF;IAAAA,iBAAI;IAENA,wFA2BM;;;;;;;IAnFFA,eACF;IADEA,0DACF;IAWIA,eAME;IANFA,kIAME;IAEFA,eACF;IADEA,wKACF;IAQEA,eAME;IANFA,mIAME;IAEFA,eACF;IADEA,wKACF;IAWAA,eACF;IADEA,0EACF;IAGEA,eACF;IADEA,wEACF;IAEyBA,eAA+C;IAA/CA,wEAA+C;;;;;;;;;ADvc5E,OAAM,MAAOO,qBAAqB;EAoD9BC,YACWC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,aAA2B,EAC3BC,eAA+B,EAC/BC,aAAoB,EACpBC,iBAAmC,EACnCC,oBAAyC,EACxCC,aAAuB,EACvBC,aAA4B;IAT7B,WAAM,GAANT,MAAM;IACN,YAAO,GAAPC,OAAO;IACP,uBAAkB,GAAlBC,kBAAkB;IAClB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,yBAAoB,GAApBC,oBAAoB;IACnB,kBAAa,GAAbC,aAAa;IACb,kBAAa,GAAbC,aAAa;IAzDlB,yBAAoB,GAAkB,IAAI;IAG1C,gBAAW,GAAG,EAAE;IAChB,kBAAa,GAAG,EAAE;IAClB,gBAAW,GAAG,EAAE;IAChB,oBAAe,GAAG,EAAE;IACpB,qBAAgB,GAAG,EAAE;IACrB,oBAAe,GAAc,EAAE;IAE/B,kBAAa,GAAG,EAAE;IAClB,qBAAgB,GAAG,EAAE;IACrB,mBAAc,GAAG,EAAE;IAGnB,mBAAc,GAA8B,IAAI;IAEhD,eAAU,GAAG,IAAI;IACjB,WAAM,GAAG,KAAK;IACd,eAAU,GAAG,KAAK;IAezB;IAEA;IAEO,2BAAsB,GAAG,EAAE;IAE3B,iBAAY,GAAG,IAAI;IAEnB,YAAO,GAAG,KAAK;IAEtB;IACO,qBAAgB,GAAkB,IAAI;IA8UtC,qBAAgB,GAAqB;MACxCC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE;KACb;IAgEM,iBAAY,GAAG,IAAI;IA6IP,aAAQ,GAAGC,QAAQ;IAnhBlC,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACL,YAAY,GAAG,IAAI,CAACX,MAAM,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACtE,IAAI,CAACjB,kBAAkB,CAACkB,aAAa,CAAC,IAAI,CAACT,YAAY,CAAC,CAACU,SAAS,CAC7DC,GAAG,IAAI;MACJ,IAAI,CAACC,cAAc,GAAGD,GAAG;MAEzB,IAAI,CAACE,oBAAoB,GAAGF,GAAG,CAACG,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAI;QAClD,IAAI,IAAI,CAACJ,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC,MAAM,CAAC,KAAKjC,SAAS,CAACsC,gBAAgB,CAACC,gBAAgB,EAAE;UACpG,OAAOF,KAAK,CAACG,IAAI,KAAKxC,SAAS,CAACsC,gBAAgB,CAACG,MAAM;SAC1D,MAAM;UACH,OAAOJ,KAAK,CAACG,IAAI,KAAKxC,SAAS,CAACsC,gBAAgB,CAACI,MAAM;;MAE/D,CAAC,CAAC,EAAEC,EAAE;MAEN,IAAI,CAACC,MAAM,GAAGZ,GAAG,CAACa,kBAAkB,KAAK,CAAC;MAC1C,IAAI,CAACC,QAAQ,GAAGd,GAAG,CAACe,KAAK,CAACC,MAAM,CAACL,EAAE;MACnC5B,aAAa,CAACkC,QAAQ,CAACjB,GAAG,CAACkB,IAAI,CAAC;MAEhC,IAAI,CAACC,aAAa,GAAG;QACjBC,WAAW,EAAEpB,GAAG,CAACkB,IAAI;QACrBG,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACRd,IAAI,EAAE,EAAE;UACRe,KAAK,EAAE,CACH;YACIL,IAAI,EAAE,IAAI,CAAClC,iBAAiB,CAACwC,OAAO,CAAC,SAAS,CAAC;YAC/CC,MAAM,EAAE;WACX,EACD;YACIP,IAAI,EAAE,IAAI,CAAClC,iBAAiB,CAACwC,OAAO,CAAC,gBAAgB,CAAC;YACtDC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACT,EACD;YACIR,IAAI,EAAElB,GAAG,CAACkB,IAAI;YACdO,MAAM,EAAE;WACX,EACD;YACIP,IAAI,EAAE,IAAI,CAAClC,iBAAiB,CAACwC,OAAO,CAAC,eAAe,CAAC;YACrDC,MAAM,EAAE;WACX;;OAGZ;MAED,IAAI,CAACE,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC,CAAC,CAAC;EACV;EAEAC,QAAQ,IACR;EAEAC,kBAAkB;IACdC,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,UAAU,GAAI,IAAI,CAACC,eAAe,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC,CAACG,MAAM,GAAG,CAAC,IAAK,IAAI,CAACC,sBAAsB,CAACD,MAAM,GAAG,CAAC;IACtI,CAAC,EAAE,CAAC,CAAC;EACT;EAEAE,kBAAkB,CAACC,cAAuB,IAAI;IAC1C,IAAIA,WAAW,EAAE;MACb,IAAI,CAACxD,eAAe,CAACyD,IAAI,EAAE;;IAI/B,IAAI,CAACtD,oBAAoB,CAACoD,kBAAkB,CAAC,IAAI,CAAChD,YAAY,CAAC,CAC1DU,SAAS,CAAEC,GAAG,IAAI;MACf,IAAI,CAACwC,gBAAgB,GAAGxC,GAAG,CAACyC,QAAQ;MACpC,IAAI,CAACT,eAAe,GAAGU,KAAK,CAACC,OAAO,CAAC3C,GAAG,CAAC4C,IAAI,CAAC,IAAI5C,GAAG,CAAC4C,IAAI,CAACT,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGnC,GAAG,CAAC4C,IAAI;MAEzF,IAAI,CAACC,aAAa,GAAG7C,GAAG,CAAC8C,SAAS,CAAC,0BAA0B,CAAC;MAC9D,IAAI,CAACC,gBAAgB,GAAG/C,GAAG,CAAC8C,SAAS,CAAC,6BAA6B,CAAC;MACpE,IAAI,CAACE,cAAc,GAAGhD,GAAG,CAAC8C,SAAS,CAAC,2BAA2B,CAAC;MAEhE,IAAI,IAAI,CAACd,eAAe,EAAE;QACtB,IAAI,CAACiB,WAAW,GAAGhB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC;QACpD,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC;QACvC,IAAI,CAACE,gBAAgB,EAAE;OAC1B,MAAM;QACH,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACC,WAAW,GAAG,EAAE;QACrB,IAAI,CAACJ,WAAW,GAAG,EAAE;QACrB,IAAI,CAACC,YAAY,GAAG,IAAI;;MAG5B,IAAI,CAACpE,eAAe,CAACwE,OAAO,EAAE;IAClC,CAAC,EAAE,MAAK,CACR,CAAC,EAAE,MAAK;MACJ,IAAI,CAAC5D,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC6D,OAAO,GAAG,IAAI,CAACN,WAAW,CAACd,MAAM,GAAG,CAAC;IAC9C,CAAC,CAAC;EACV;EAEAqB,oBAAoB,CAAClB,WAAW,GAAG,IAAI;IACnC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACxD,eAAe,CAACyD,IAAI,EAAE;;IAE/B,IAAI,CAACtD,oBAAoB,CAACwE,yBAAyB,CAAC,IAAI,CAACpE,YAAY,CAAC,CACjEU,SAAS,CAAEC,GAAG,IAAI;MACf,IAAI,CAACoC,sBAAsB,GAAGpC,GAAG,CAAC4C,IAAI;MAEtC,IAAI,CAAC9D,eAAe,CAACwE,OAAO,EAAE;IAClC,CAAC,CAAC;EACV;EAEAH,gBAAgB;IACZ,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB,IAAI,CAACK,aAAa,GAAG,IAAI,CAAC1B,eAAe,CAAC,IAAI,CAACkB,YAAY,CAAC,IAAI,EAAE;IAElE,IAAI,CAAC,IAAI,CAACQ,aAAa,IAAI,CAAC,IAAI,CAACR,YAAY,EAAE;IAE/CjB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACwB,aAAa,CAAC,CAACC,OAAO,CAAEC,YAAoB,IAAI;MAE7D,IAAI,CAAC,IAAI,CAACR,eAAe,CAACS,QAAQ,CAAC,GAAGD,YAAY,EAAE,CAAC,EAAE;QACnD,IAAI,CAACR,eAAe,CAACU,IAAI,CAAC,GAAGF,YAAY,EAAE,CAAC;;MAGhD,IAAI,CAAC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,EAAE;QACjC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,GAAG,EAAE;;MAGvC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,GAAG,CAC7B,GAAG,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,EACjC,GAAG,IAAI,CAACF,aAAa,CAACE,YAAY,CAAC,CACtC;IACL,CAAC,CAAC;EAEN;EAEAG,YAAY,CAACC,KAAK;IACd,IAAI,CAACd,YAAY,GAAGc,KAAK;IACzB,IAAI,CAACb,gBAAgB,EAAE;EAC3B;EAEAc,sBAAsB;IAClB,IAAI,CAAC/E,aAAa,CAACgF,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MAC7CC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACT,CAAC;EACN;EAEA1C,gBAAgB,CAAC2C,QAAQ,EAAEhC,cAAuB,IAAI;IAClD,IAAI,CAACD,kBAAkB,CAACC,WAAW,CAAC;IACpC,IAAI,CAACkB,oBAAoB,CAAClB,WAAW,CAAC;EAC1C;EAEAiC,eAAe,CAACjF,UAA2B,EAAEE,QAAyB,EAAEgF,OAAmB,EAAEC,SAAqB;IAC9G,IAAI,CAACxF,oBAAoB,CAACsF,eAAe,CAACjF,UAAU,EAAEE,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;MAC9E,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;MAClC8C,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAG,eAAe,CAACC,WAAW;IAEvB,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAI,CAACxB,WAAW,CAACuB,WAAW,CAAC,CAACjB,OAAO,CAAC,CAACmB,IAAI,EAAEC,KAAK,KAAI;MAElDF,YAAY,CAACC,IAAI,CAACE,YAAY,CAAC,GAAGD,KAAK;IAC3C,CAAC,CAAC;IACF,OAAOF,YAAY;EACvB;EAEAI,mBAAmB,CAACL,WAAmB,EAAEM,UAAqC,EAAEV,OAAmB,EAAEC,SAAqB;IACtH,MAAMU,QAAQ,GAAG,IAAI,CAACR,eAAe,CAACC,WAAW,CAAC;IAClD,IAAI,CAAC3F,oBAAoB,CAACgG,mBAAmB,CAACC,UAAU,CAAC,CAACnF,SAAS,CAAEC,GAAG,IAAI;MACxE,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;MAClC8C,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAY,IAAI,CAACpB,KAA4B;IAC7B,MAAMqB,eAAe,GAAGrB,KAAK,CAACsB,SAAS,CAACC,OAAO,CAACC,aAAa;IAC7D,MAAMC,aAAa,GAAGzB,KAAK,CAAC0B,iBAAiB,CAACH,OAAO,CAACC,aAAa;IACnE,MAAMG,QAAQ,GAAG3B,KAAK,CAACc,IAAI,CAACS,OAAO,CAACC,aAAa;IAEjD,MAAMI,iBAAiB,GAAGP,eAAe,CAAC,IAAI,CAAC;IAC/C,MAAMQ,eAAe,GAAGJ,aAAa,CAAC,IAAI,CAAC;IAE3C,MAAMK,aAAa,GAAGT,eAAe,CAAC,eAAe,CAAC;IACtD,MAAMU,gBAAgB,GAAGV,eAAe,CAAC,kBAAkB,CAAC;IAE5D,MAAMW,WAAW,GAAGP,aAAa,CAAC,eAAe,CAAC;IAClD,MAAMQ,cAAc,GAAGR,aAAa,CAAC,kBAAkB,CAAC;IAExD,MAAMS,WAAW,GAAGP,QAAQ,CAAC,eAAe,CAAC;IAC7C,MAAMQ,cAAc,GAAGR,QAAQ,CAAC,mBAAmB,CAAC;IACpD,MAAMS,QAAQ,GAAGT,QAAQ,CAAC,WAAW,CAAC;IAEtC,IAAIE,eAAe,KAAKD,iBAAiB,IAAI5B,KAAK,CAACqC,YAAY,KAAKrC,KAAK,CAACsC,aAAa,EAAE;IAEzF,IAAIV,iBAAiB,KAAK,gBAAgB,EAAE;MACxC9H,iBAAiB,CACbkG,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAACsC,aAAa,EACnBtC,KAAK,CAACqC,YAAY,CACrB;MAED,IAAIT,iBAAiB,KAAKC,eAAe,EAAE;MAE3C,IAAI,CAACtB,eAAe,CAAC4B,cAAc,EAAE,IAAI,CAACI,WAAW,CAACN,cAAc,CAAC,EAAE,MAAK;QACxEnI,iBAAiB,CACbkG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACqC,YAAY,EAClBrC,KAAK,CAACsC,aAAa,CACtB;QACD,IAAI,CAACnH,aAAa,CAACuF,KAAK,CAAC,IAAI,CAAC1F,iBAAiB,CAACwC,OAAO,CAAC,6BAA6B,CAAC,CAAC;MAC3F,CAAC,EAAE,MAAK;QACJ,IAAI,CAACrC,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACxH,iBAAiB,CAACwC,OAAO,CAAC,iCAAiC,CAAC,CAAC;MACjG,CAAC,CAAC;KACL,MAAM;MACH,IAAIwC,KAAK,CAAC0B,iBAAiB,KAAK1B,KAAK,CAACsB,SAAS,EAAE;QAC7CzH,eAAe,CAACmG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EAAEoB,KAAK,CAACsC,aAAa,EAAEtC,KAAK,CAACqC,YAAY,CAAC;QAC9E,IAAI,CAACpB,mBAAmB,CAACW,iBAAiB,EAAE;UACxCa,SAAS,EAAE,IAAI,CAAC9B,eAAe,CAACiB,iBAAiB,CAAC;UAClDc,WAAW,EAAEX,gBAAgB;UAC7BY,gBAAgB,EAAEV,cAAc;UAChCW,QAAQ,EAAEd,aAAa;UACvBe,aAAa,EAAEb,WAAW;UAC1Bc,aAAa,EAAE,IAAI,CAACzH,YAAY;UAChC0H,SAAS,EAAE,IAAI,CAACR,WAAW,CAACR,gBAAgB,CAAC;UAC7CiB,cAAc,EAAE,IAAI,CAACT,WAAW,CAACN,cAAc;SAClD,EAAE,MAAK;UACJpI,eAAe,CAACmG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EAAEoB,KAAK,CAACqC,YAAY,EAAErC,KAAK,CAACsC,aAAa,CAAC;UAC9E,IAAI,CAACnH,aAAa,CAACuF,KAAK,CAAC,IAAI,CAAC1F,iBAAiB,CAACwC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChG,CAAC,EAAE,MAAK;UACJ,IAAI,CAACrC,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACxH,iBAAiB,CAACwC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACtG,CAAC,CAAC;OACL,MAAM;QACH1D,iBAAiB,CACbkG,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAACsC,aAAa,EACnBtC,KAAK,CAACqC,YAAY,CACrB;QAED,IAAI,CAACpB,mBAAmB,CAACW,iBAAiB,EACtC;UACIa,SAAS,EAAE,IAAI,CAAC9B,eAAe,CAACiB,iBAAiB,CAAC;UAClDc,WAAW,EAAEX,gBAAgB;UAC7BY,gBAAgB,EAAEd,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGI,cAAc;UAC9EW,QAAQ,EAAEd,aAAa;UACvBe,aAAa,EAAEhB,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGG,WAAW;UACxEc,aAAa,EAAE,IAAI,CAACzH,YAAY;UAChC0H,SAAS,EAAE,IAAI,CAACR,WAAW,CAACR,gBAAgB,CAAC;UAC7CiB,cAAc,EAAE,IAAI,CAACT,WAAW,CAACN,cAAc;SAClD,EACD,MAAK;UACDnI,iBAAiB,CACbkG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACqC,YAAY,EAClBrC,KAAK,CAACsC,aAAa,CACtB;UACD,IAAI,CAACnH,aAAa,CAACuF,KAAK,CAAC,IAAI,CAAC1F,iBAAiB,CAACwC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChG,CAAC,EAAE,MAAK;UACJ,IAAI,CAACrC,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACxH,iBAAiB,CAACwC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACtG,CAAC,CAAC;;;EAGlB;EAEAyF,YAAY,CAACC,IAAY;IACrB;IACA,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IAC3B,MAAMG,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMC,OAAO,GAAGN,IAAI,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAChC;EAEAlB,WAAW,CAACG,WAAW;IACnB,OAAO,IAAI,CAAClE,gBAAgB,CAAC,SAAS,CAAC,CAACpC,IAAI,CAAE0E,IAAI,IAAI;MAClD,OAAOA,IAAI,CAACgC,aAAa,KAAK,CAAC,IAAI,CAACzH,YAAY,IAAIyF,IAAI,CAAC4B,WAAW,KAAK,CAACA,WAAW,IAAI5B,IAAI,CAAC6C,UAAU,KAAK,IAAI,CAACzE,YAAY;IAClI,CAAC,CAAC,EAAEvC,EAAE;EACV;EAEAiH,QAAQ,CAACxI,UAAU,EAAEI,QAAQ;IACzB,IAAI,CAACqI,cAAc,GAAG;MAClBxI,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BI,QAAQ,EAAEL,UAAU;MACpB+H,IAAI,EAAE,IAAI,CAACjE,YAAY;MACvB1D;KACH;IACD,IAAI,CAACN,aAAa,CAACgF,IAAI,CAAC,IAAI,CAAC4D,iBAAiB,EAAE;MAC5C1D,QAAQ,EAAE;KACb,CAAC;EACN;EAEA2D,UAAU,CAACnD,WAAW;IAElB,MAAMxF,UAAU,GAAG,IAAI,CAACoD,gBAAgB,CAAC,WAAW,CAAC,CAACoC,WAAW,CAAC,CAACjE,EAAE;IACrE,MAAMnB,QAAQ,GAAG,IAAI,CAAC+G,WAAW,CAACnH,UAAU,CAAC;IAC7C,MAAM4I,WAAW,GAAG,IAAI,CAAC3E,WAAW,CAACuB,WAAW,CAAC,CAACqD,GAAG,CAAEnD,IAAI,IAAKA,IAAI,CAACE,YAAY,CAAC;IAElFjH,IAAI,CAACmK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACnJ,iBAAiB,CAACwC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACpJ,iBAAiB,CAACwC,OAAO,CAAC,gDAAgD,CAAC;MACtF6G,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE;KACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACzJ,oBAAoB,CAAC0J,cAAc,CAAC,IAAI,CAACtJ,YAAY,EAAED,UAAU,EAAE4I,WAAW,EAAExI,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;UAC7G,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;UAClC,IAAI,CAACxC,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACxH,iBAAiB,CAACwC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAChG,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAUAoH,iBAAiB,CAACxJ,UAAU,EAAEG,cAAc,EAAEC,QAAQ;IAClD,IAAI,CAACqJ,gBAAgB,GAAG;MACpB,GAAG,IAAI,CAACA,gBAAgB;MACxBzJ,UAAU;MACVC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BE,cAAc;MACdC;KACH;IAED,IAAI,CAACN,aAAa,CAACgF,IAAI,CAAC,IAAI,CAAC4E,cAAc,EAAE;MACzC1E,QAAQ,EAAE;KAEb,CAAC;EAEN;EAEA;EACA2E,cAAc,CAAC/E,KAAiB,EAAEc,IAAS;IAEvCd,KAAK,CAACgF,cAAc,EAAE;IACtBhF,KAAK,CAACiF,eAAe,EAAE;IAEvB,IAAIjF,KAAK,CAACkF,MAAM,CAAC,WAAW,CAAC,KAAK,aAAa,EAAE;MAC7C;;IAGJ,MAAMC,UAAU,GAAG,IAAI,CAACC,aAAa,CAACtE,IAAI,CAAC;IAE3C;IACA,IAAI,IAAI,CAACuE,gBAAgB,KAAKF,UAAU,EAAE;MACtC,IAAI,CAACE,gBAAgB,GAAG,IAAI;KAC/B,MAAM;MACH;MACA,IAAI,CAACA,gBAAgB,GAAGF,UAAU;;EAE1C;EAEAG,cAAc,CAACxE,IAAS;IACpB,MAAMqE,UAAU,GAAG,IAAI,CAACC,aAAa,CAACtE,IAAI,CAAC;IAC3C,OAAO,IAAI,CAACuE,gBAAgB,KAAKF,UAAU;EAC/C;EAEAC,aAAa,CAACtE,IAAS;IACnB;IACA,OAAO,GAAGA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACtE,IAAI,IAAIsE,IAAI,CAAC8B,QAAQ,IAAI,aAAa,EAAE;EAChF;EAEA2C,iBAAiB;IACb,IAAI,CAACF,gBAAgB,GAAG,IAAI;EAChC;EAGAG,eAAe,CAACxF,KAAiB;IAC7B,MAAMkF,MAAM,GAAGlF,KAAK,CAACkF,MAAqB;IAE1C;IACA,IAAI,CAACA,MAAM,CAACO,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAACP,MAAM,CAACO,OAAO,CAAC,WAAW,CAAC,EAAE;MACnE,IAAI,CAACF,iBAAiB,EAAE;;EAEhC;EAKA;EACAG,WAAW,CAAC5E,IAAS;IACjB,IAAI,CAACyE,iBAAiB,EAAE;IAExB,IAAI,CAACI,YAAY,GAAG7E,IAAI;IAExB,IAAI,CAAC5F,aAAa,CAACgF,IAAI,CAAC,IAAI,CAAC0F,gBAAgB,EAAE;MAC3CxF,QAAQ,EAAE;KACb,CAAC;EAEN;EAEAyF,oBAAoB,CAAC/E,IAAS,EAAEtF,QAAQ;IACpC,MAAMsK,WAAW,GAAG;MAChBC,KAAK,EAAE,kDAAkD;MACzDC,KAAK,EAAE;KACV;IAED,MAAMC,cAAc,GAAG;MACnBF,KAAK,EAAE,iCAAiC;MACxCC,KAAK,EAAE;KACV;IAED,MAAME,YAAY,GAAG;MACjBH,KAAK,EAAE,6BAA6B;MACpCC,KAAK,EAAE;KACV;IAGDjM,IAAI,CAACmK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACnJ,iBAAiB,CAACwC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACpJ,iBAAiB,CAACwC,OAAO,CAACsI,WAAW,CAAChF,IAAI,CAACtE,IAAI,CAAC,CAAC;MAC5D6H,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpB4B,iBAAiB,EAAE,IAAI,CAACnL,iBAAiB,CAACwC,OAAO,CAAC,KAAK,CAAC;MACxD4I,gBAAgB,EAAE,IAAI,CAACpL,iBAAiB,CAACwC,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACgH,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACnE,eAAe,CAACO,IAAI,CAACE,YAAY,EAAExF,QAAQ,EAAE,MAAK;UACnD,IAAI,CAACL,aAAa,CAACuF,KAAK,CAAC,IAAI,CAAC1F,iBAAiB,CAACwC,OAAO,CAAC0I,YAAY,CAACpF,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC;QACrF,CAAC,EAAE,MAAK;UACJ,IAAI,CAACrB,aAAa,CAACqH,OAAO,CAAC,IAAI,CAACxH,iBAAiB,CAACwC,OAAO,CAACyI,cAAc,CAACnF,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC;;IAEV,CAAC,CAAC;IAEF,IAAI,CAAC+I,iBAAiB,EAAE;EAC5B;EAEAc,eAAe,CAACvF,IAAS,EAAEtF,QAAgB;IAEvC,IAAI,CAAC+J,iBAAiB,EAAE;IAExB,IAAI,CAACV,gBAAgB,GAAG;MACpBvJ,UAAU,EAAEwF,IAAI,CAACE,YAAY;MAC7B5F,UAAU,EAAE0F,IAAI,CAAC4B,WAAW;MAC5BrH,YAAY,EAAEyF,IAAI,CAACgC,aAAa;MAChCgD,WAAW,EAAEhF,IAAI,CAACgF,WAAW;MAC7BQ,cAAc,EAAExF,IAAI,CAACyF,eAAe;MACpC/K,QAAQ,EAAEA;KACb;IAED,IAAI,CAACN,aAAa,CAACgF,IAAI,CAAC,IAAI,CAAC4E,cAAc,EAAE;MACzC1E,QAAQ,EAAE;KACb,CAAC;EAEN;EAEAoG,aAAa;IACTzM,IAAI,CAACmK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACnJ,iBAAiB,CAACwC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACpJ,iBAAiB,CAACwC,OAAO,CAAC,sCAAsC,CAAC;MAC5E6G,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpB4B,iBAAiB,EAAE,IAAI,CAACnL,iBAAiB,CAACwC,OAAO,CAAC,gBAAgB,CAAC;MACnE4I,gBAAgB,EAAE,IAAI,CAACpL,iBAAiB,CAACwC,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACgH,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACzJ,oBAAoB,CAACuL,aAAa,CAAC,IAAI,CAACnL,YAAY,CAAC,CAACU,SAAS,CAAEC,GAAG,IAAI;UACzEjC,IAAI,CAACmK,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAACnJ,iBAAiB,CAACwC,OAAO,CAAC,UAAU,CAAC;YACjD4G,IAAI,EAAE,IAAI,CAACpJ,iBAAiB,CAACwC,OAAO,CAAC,4BAA4B,CAAC;YAClE6G,IAAI,EAAE;WACT,CAAC;UACF,IAAI,CAAC1G,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;QACtC,CAAC,EAAG+C,KAAK,IAAI;UACT3G,IAAI,CAACmK,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAACnJ,iBAAiB,CAACwC,OAAO,CAAC,QAAQ,CAAC;YAC/C4G,IAAI,EAAE,IAAI,CAACpJ,iBAAiB,CAACwC,OAAO,CAAC,4BAA4B,CAAC;YAClE6G,IAAI,EAAE;WACT,CAAC;QACN,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAEAoC,kBAAkB,CAACC,eAAuB,EAAElK,IAAkC,EAAEmK,WAAoB;IAChG,QAAQnK,IAAI;MACR,KAAK,MAAM;QACP,OAAO,IAAI,CAACqC,aAAa,CAAC6H,eAAe,CAAC,EAAEtK,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAAC8F,OAAO,KAAKD,WAAW,CAAC;MAC1F,KAAK,SAAS;QACV,OAAO,IAAI,CAAC5H,gBAAgB,CAAC2H,eAAe,CAAC,EAAEtK,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAAC+F,UAAU,KAAKF,WAAW,CAAC;MAChG,KAAK,OAAO;QACR,OAAO,IAAI,CAAC9H,aAAa,CAAC6H,eAAe,CAAC,IAAI,IAAI,CAAC3H,gBAAgB,CAAC2H,eAAe,CAAC;IAAC;EAEjG;EAEAI,WAAW;IACP,IAAI,CAAClK,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1B,IAAI,CAAC3B,oBAAoB,CAAC8L,8BAA8B,CAAC,IAAI,CAAC1L,YAAY,CAAC,CAACU,SAAS,CAAEC,GAAG,IAAI;MAC1F,IAAI,CAACb,aAAa,CAACqH,OAAO,CAACxG,GAAG,CAACgL,OAAO,CAAC;IAC3C,CAAC,EAAGtG,KAAK,IAAI;MAET,IAAI,CAAC9D,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1B,IAAI,CAACzB,aAAa,CAACuF,KAAK,CAACA,KAAK,CAACsG,OAAO,CAAC;IAC3C,CAAC,CAAC;EACN;EAEAC,QAAQ,CAACC,SAAS;IACd,IAAI,CAAC3B,iBAAiB,EAAE;IACxB,IAAI,CAAC1K,aAAa,CAACsM,SAAS,CAACD,SAAS,CAAC,CAACnL,SAAS,CAAEC,GAAG,IAAI;MACtD,IAAI,CAACb,aAAa,CAACqH,OAAO,CAAC,0BAA0B,CAAC;MACtD,IAAI,CAAC7E,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAG+C,KAAK,IAAI;MACT,IAAI,CAACvF,aAAa,CAACuF,KAAK,CAACA,KAAK,CAACsG,OAAO,IAAI,uBAAuB,CAAC;IACtE,CAAC,CAAC;EACN;EAEAI,YAAY;IACR,IAAI,CAACvM,aAAa,CAACwM,mBAAmB,CAAC,IAAI,CAACnL,oBAAoB,CAAC,CAACH,SAAS,CAAEC,GAAG,IAAI;MAChF,IAAI,CAACb,aAAa,CAACqH,OAAO,CAAC,qCAAqC,CAAC;MACjE,IAAI,CAAC7E,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAG+C,KAAK,IAAI;MACT,IAAI,CAACvF,aAAa,CAACuF,KAAK,CAACA,KAAK,CAAC4G,OAAO,IAAI5G,KAAK,CAACsG,OAAO,IAAI,kCAAkC,CAAC;IAClG,CAAC,CAAC;EACN;EAAC;qBAjlBQxM,qBAAqB;EAAA;EAAA;UAArBA,qBAAqB;IAAA+M;IAAAC;MAAA;;;;;;;;;;;;;;;;;iBAArBC,2BAAuB;QAAA;;;;;;;;QCvBpCxN,8BAA+C;QAG3CA,wCAAyE;QACzEA,8BAAwB;QAIMA,YAAwB;;QAAAA,iBAAQ;QACxDA,oCAQC;QAHCA;UAAA;QAAA,EAA0B;UAAA,OAChBwN,wBAAoB;QAAA,EADJ;;QAI1BxN,oFAEY;QACdA,iBAAY;QAEdA,+BAAwD;QAGpDA;UAAA,OAASwN,4BAAwB;QAAA,EAAC;QAGlCxN,yBAA6C;QAC7CA,+BACF;QAAAA,iBAAS;QACTA,mCAOC;QALCA;UAAA,OAASwN,iBAAa;QAAA,EAAC;QAMvBxN,yBAEK;QAEPA,iBAAS;QACTA,mCAIC;QAFCA;UAAA,OAASwN,mBAAe;QAAA,EAAC;QAGzBxN,yBAA8B;QAEhCA,iBAAS;QAIfA,gCAAsB;QAElBA,yEA6MM;QACNA,2FAEe;QACfA,2FAEe;QACjBA,iBAAM;QACNA,gCAA2C;QAIrBA,4BAAW;QAAAA,iBAAI;QAC7BA,8BAAiB;QACfA,yFAEF;QAAAA,iBAAI;QAENA,gCAMC;QAFCA;UAAA,OAAsBwN,gBAAY;QAAA,EAAC;QAGnCxN,2FAmBe;QACfA,2FAWe;QACjBA,iBAAM;QAOlBA,0HAOc;QACdA,0HAKc;QACdA,0HAKc;QAEdA,0HAMc;QAEdA,0HAgBc;QAEdA,0HAYc;QAEdA,2HA6Bc;QAEdA,4HAuFc;;;QAvfUA,eAA+B;QAA/BA,iDAA+B;QAKrBA,eAAwB;QAAxBA,mDAAwB;QAK9CA,eAA6C;QAA7CA,8EAA6C;QAF7CA,iCAAmB;QAOSA,eAAc;QAAdA,yCAAc;QAS1CA,eAAmB;QAAnBA,qCAAmB;QAQnBA,eAGE;QAHFA,8EAGE;QAGAA,eAAkE;QAAlEA,oFAAkE;QAOpEA,eAAmB;QAAnBA,qCAAmB;QAUmBA,eAAa;QAAbA,kCAAa;QA8MxCA,eAA6B;QAA7BA,sDAA6B;QAG7BA,eAAgB;QAAhBA,qCAAgB;QAiBzBA,eAA0C;QAA1CA,4DAA0C;QAI3BA,eAAgB;QAAhBA,qCAAgB;QAoBhBA,eAAiB;QAAjBA,sCAAiB", "names": ["moveItemInArray", "transferArrayItem", "<PERSON><PERSON>", "AppConfig", "i0", "ctx_r28", "ctx_r45", "ctx_r48", "ctx_r52", "ctx_r55", "ctx_r57", "AutoScheduleComponent", "constructor", "_route", "_router", "_tournamentService", "_stageService", "_loadingService", "_titleService", "_translateService", "_autoScheduleService", "_modalService", "_toastService", "locationId", "tournamentId", "timeSlotId", "lastTimeSlotId", "configId", "location", "isFetching", "snapshot", "paramMap", "get", "getTournament", "subscribe", "res", "tournamentInfo", "leagueOrGroupStageId", "stages", "find", "stage", "TOURNAMENT_TYPES", "groups_knockouts", "type", "groups", "league", "id", "isLock", "is_locked_schedule", "seasonId", "group", "season", "setTitle", "name", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "instant", "isLink", "link", "onScheduleAction", "ngOnInit", "ngAfterViewChecked", "setTimeout", "hasMatches", "responseMatches", "Object", "keys", "length", "listUnScheduledMatches", "getScheduleMatches", "showLoading", "show", "responseMetadata", "metadata", "Array", "isArray", "data", "teamConflicts", "conflicts", "referee<PERSON><PERSON><PERSON><PERSON><PERSON>", "stageConflicts", "dateOptions", "selectedDate", "mapListLocations", "listLocationIds", "listMatches", "dismiss", "hasPlan", "getUnScheduleMatches", "getListUnScheduledMatches", "listLocations", "for<PERSON>ach", "locationName", "includes", "push", "onSelectDate", "event", "openModalSetupSchedule", "open", "modalSetupSchedule", "centered", "size", "response", "unScheduleMatch", "onError", "onSuccess", "error", "mapNewSlotIndex", "locationKey", "newSlotIndex", "item", "index", "time_slot_id", "updateLocationMatch", "updateData", "newIndex", "drop", "targetContainer", "container", "element", "nativeElement", "prevContainer", "previousContainer", "dragItem", "targetContainerId", "prevContainerId", "targetStageId", "targetLocationId", "prevStageId", "prevLocationId", "itemStageId", "itemTimeSlotId", "itemType", "currentIndex", "previousIndex", "getConfigId", "success", "new_index", "location_id", "prev_location_id", "stage_id", "prev_stage_id", "tournament_id", "config_id", "prev_config_id", "getShortTime", "time", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "begin_date", "editPlan", "selectedConfig", "modalEditSchedule", "deletePlan", "timeSlotIds", "map", "fire", "title", "text", "icon", "showCancelButton", "reverseButtons", "then", "result", "isConfirmed", "deleteSchedule", "openModalAddBreak", "breakModalParams", "modalCrudBreak", "toggleDropdown", "preventDefault", "stopPropagation", "target", "dropdownId", "getDropdownId", "activeDropdownId", "isDropdownOpen", "closeAllDropdowns", "onDocumentClick", "closest", "onEditMatch", "selectedItem", "modalUpdateMatch", "onUnscheduleTimeSlot", "description", "match", "break", "successMessage", "errorMessage", "confirmButtonText", "cancelButtonText", "onEditEventTime", "breakDurations", "break_durations", "clearSchedule", "isMatchHasConflict", "scheduleMatchId", "itemCheckId", "team_id", "referee_id", "onClickLock", "updateTournamentScheduleStatus", "message", "swapTeam", "matchInfo", "swapTeams", "autoGenerate", "autoGenerateMatches", "warning", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.html"], "sourcesContent": ["import { Component, HostListener, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TournamentService } from '../../../services/tournament.service';\r\nimport { LoadingService } from '../../../services/loading.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService, UpdateLocationMatchParams } from '../../../services/auto-schedule.service';\r\nimport { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\nimport { BreakModalParams } from \"./modal-crud-break/modal-crud-break.component\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { AppConfig } from \"../../../app-config\";\r\nimport { StageService } from \"../../../services/stage.service\";\r\nimport { UpdateConfigParams } from './modal-update-config/modal-update-config.component';\r\nimport config from '../../../../../capacitor.config';\r\n\r\n\r\n@Component({\r\n    selector: 'app-auto-schedule',\r\n    templateUrl: './auto-schedule.component.html',\r\n    styleUrls: ['./auto-schedule.component.scss']\r\n})\r\nexport class AutoScheduleComponent {\r\n\r\n    public contentHeader: object;\r\n    public tournamentId: any;\r\n    public seasonId: any;\r\n    public leagueOrGroupStageId: number | null = null;\r\n    public tournamentInfo: null;\r\n\r\n    public dateOptions = [];\r\n    public listLocations = {};\r\n    public listMatches = {};\r\n    public listLocationIds = [];\r\n    public responseMetadata = {};\r\n    public responseMatches: {} | null = {}\r\n\r\n    public teamConflicts = {};\r\n    public refereeConflicts = {};\r\n    public stageConflicts = {};\r\n\r\n\r\n    public selectedConfig: UpdateConfigParams | null = null;\r\n\r\n    public isFetching = true;\r\n    public isLock = false;\r\n    public hasMatches = false;\r\n\r\n\r\n    @ViewChild('modalSetupSchedule')\r\n    modalSetupSchedule!: TemplateRef<any>;\r\n\r\n    @ViewChild('modalEditSchedule')\r\n    modalEditSchedule!: TemplateRef<any>;\r\n\r\n    @ViewChild('modalCrudBreak')\r\n    modalCrudBreak!: TemplateRef<any>;\r\n\r\n    @ViewChild('modalUpdateMatch')\r\n    modalUpdateMatch: TemplateRef<any>;\r\n\r\n    // public listUnScheduledMatches = [\r\n\r\n    // ];\r\n\r\n    public listUnScheduledMatches = [];\r\n\r\n    public selectedDate = null;\r\n\r\n    public hasPlan = false;\r\n\r\n    // Simple dropdown state - track which dropdown is open\r\n    public activeDropdownId: string | null = null;\r\n\r\n    constructor(\r\n        public _route: ActivatedRoute,\r\n        public _router: Router,\r\n        public _tournamentService: TournamentService,\r\n        public _stageService: StageService,\r\n        public _loadingService: LoadingService,\r\n        public _titleService: Title,\r\n        public _translateService: TranslateService,\r\n        public _autoScheduleService: AutoScheduleService,\r\n        private _modalService: NgbModal,\r\n        private _toastService: ToastrService,\r\n    ) {\r\n        this.isFetching = true;\r\n        this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\r\n        this._tournamentService.getTournament(this.tournamentId).subscribe(\r\n            (res) => {\r\n                this.tournamentInfo = res;\r\n\r\n                this.leagueOrGroupStageId = res.stages.find((stage) => {\r\n                    if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\r\n                    } else {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.league;\r\n                    }\r\n                })?.id;\r\n\r\n                this.isLock = res.is_locked_schedule === 1;\r\n                this.seasonId = res.group.season.id;\r\n                _titleService.setTitle(res.name);\r\n\r\n                this.contentHeader = {\r\n                    headerTitle: res.name,\r\n                    actionButton: false,\r\n                    breadcrumb: {\r\n                        type: '',\r\n                        links: [\r\n                            {\r\n                                name: this._translateService.instant('Leagues'),\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Manage Leagues'),\r\n                                isLink: true,\r\n                                link: '/leagues/manage'\r\n                            },\r\n                            {\r\n                                name: res.name,\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Auto Schedule'),\r\n                                isLink: false\r\n                            }\r\n                        ]\r\n                    }\r\n                };\r\n\r\n                this.onScheduleAction(null, true)\r\n            });\r\n    }\r\n\r\n    ngOnInit() {\r\n    }\r\n\r\n    ngAfterViewChecked() {\r\n        setTimeout(() => {\r\n            this.hasMatches = (this.responseMatches && Object.keys(this.responseMatches).length > 0) || this.listUnScheduledMatches.length > 0;\r\n        }, 0)\r\n    }\r\n\r\n    getScheduleMatches(showLoading: boolean = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n\r\n\r\n        this._autoScheduleService.getScheduleMatches(this.tournamentId)\r\n            .subscribe((res) => {\r\n                this.responseMetadata = res.metadata;\r\n                this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\r\n\r\n                this.teamConflicts = res.conflicts['team_scheduling_conflict'];\r\n                this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\r\n                this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\r\n\r\n                if (this.responseMatches) {\r\n                    this.dateOptions = Object.keys(this.responseMatches);\r\n                    this.selectedDate = this.dateOptions[0];\r\n                    this.mapListLocations();\r\n                } else {\r\n                    this.listLocationIds = [];\r\n                    this.listMatches = {};\r\n                    this.dateOptions = [];\r\n                    this.selectedDate = null;\r\n                }\r\n\r\n                this._loadingService.dismiss();\r\n            }, () => {\r\n            }, () => {\r\n                this.isFetching = false\r\n                this.hasPlan = this.dateOptions.length > 0;\r\n            });\r\n    }\r\n\r\n    getUnScheduleMatches(showLoading = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n        this._autoScheduleService.getListUnScheduledMatches(this.tournamentId)\r\n            .subscribe((res) => {\r\n                this.listUnScheduledMatches = res.data;\r\n\r\n                this._loadingService.dismiss();\r\n            });\r\n    }\r\n\r\n    mapListLocations() {\r\n        this.listLocationIds = [];\r\n        this.listMatches = {};\r\n\r\n        this.listLocations = this.responseMatches[this.selectedDate] || {};\r\n\r\n        if (!this.listLocations || !this.selectedDate) return;\r\n\r\n        Object.keys(this.listLocations).forEach((locationName: string) => {\r\n\r\n            if (!this.listLocationIds.includes(`${locationName}`)) {\r\n                this.listLocationIds.push(`${locationName}`);\r\n            }\r\n\r\n            if (!this.listMatches[locationName]) {\r\n                this.listMatches[locationName] = [];\r\n            }\r\n\r\n            this.listMatches[locationName] = [\r\n                ...this.listMatches[locationName],\r\n                ...this.listLocations[locationName]\r\n            ]\r\n        });\r\n\r\n    }\r\n\r\n    onSelectDate(event) {\r\n        this.selectedDate = event;\r\n        this.mapListLocations();\r\n    }\r\n\r\n    openModalSetupSchedule() {\r\n        this._modalService.open(this.modalSetupSchedule, {\r\n            centered: true,\r\n            size: 'lg'\r\n        });\r\n    }\r\n\r\n    onScheduleAction(response, showLoading: boolean = true) {\r\n        this.getScheduleMatches(showLoading);\r\n        this.getUnScheduleMatches(showLoading);\r\n    }\r\n\r\n    unScheduleMatch(timeSlotId: string | number, configId: string | number, onError: () => void, onSuccess: () => void) {\r\n        this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe((res) => {\r\n            this.onScheduleAction(null, false);\r\n            onSuccess && onSuccess()\r\n        }, (error) => {\r\n            onError();\r\n        })\r\n    }\r\n\r\n\r\n    mapNewSlotIndex(locationKey) {\r\n\r\n        const newSlotIndex = {};\r\n        this.listMatches[locationKey].forEach((item, index) => {\r\n\r\n            newSlotIndex[item.time_slot_id] = index;\r\n        });\r\n        return newSlotIndex;\r\n    }\r\n\r\n    updateLocationMatch(locationKey: string, updateData: UpdateLocationMatchParams, onError: () => void, onSuccess: () => void) {\r\n        const newIndex = this.mapNewSlotIndex(locationKey);\r\n        this._autoScheduleService.updateLocationMatch(updateData).subscribe((res) => {\r\n            this.onScheduleAction(null, false);\r\n            onSuccess && onSuccess();\r\n        }, (error) => {\r\n            onError();\r\n        });\r\n    }\r\n\r\n\r\n    drop(event: CdkDragDrop<string[]>) {\r\n        const targetContainer = event.container.element.nativeElement;\r\n        const prevContainer = event.previousContainer.element.nativeElement;\r\n        const dragItem = event.item.element.nativeElement;\r\n\r\n        const targetContainerId = targetContainer['id'];\r\n        const prevContainerId = prevContainer['id'];\r\n\r\n        const targetStageId = targetContainer['data_stage_id'];\r\n        const targetLocationId = targetContainer['data_location_id'];\r\n\r\n        const prevStageId = prevContainer['data_stage_id'];\r\n        const prevLocationId = prevContainer['data_location_id'];\r\n\r\n        const itemStageId = dragItem['data_stage_id'];\r\n        const itemTimeSlotId = dragItem['data_time_slot_id'];\r\n        const itemType = dragItem['data_type'];\r\n\r\n        if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\r\n\r\n        if (targetContainerId === 'unScheduleZone') {\r\n            transferArrayItem(\r\n                event.previousContainer.data,\r\n                event.container.data,\r\n                event.previousIndex,\r\n                event.currentIndex\r\n            );\r\n\r\n            if (targetContainerId === prevContainerId) return;\r\n\r\n            this.unScheduleMatch(itemTimeSlotId, this.getConfigId(prevLocationId), () => {\r\n                transferArrayItem(\r\n                    event.container.data,\r\n                    event.previousContainer.data,\r\n                    event.currentIndex,\r\n                    event.previousIndex\r\n                );\r\n                this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\r\n            }, () => {\r\n                this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\r\n            });\r\n        } else {\r\n            if (event.previousContainer === event.container) {\r\n                moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\r\n                this.updateLocationMatch(targetContainerId, {\r\n                    new_index: this.mapNewSlotIndex(targetContainerId),\r\n                    location_id: targetLocationId,\r\n                    prev_location_id: prevLocationId,\r\n                    stage_id: targetStageId,\r\n                    prev_stage_id: prevStageId,\r\n                    tournament_id: this.tournamentId,\r\n                    config_id: this.getConfigId(targetLocationId),\r\n                    prev_config_id: this.getConfigId(prevLocationId),\r\n                }, () => {\r\n                    moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\r\n                    this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                });\r\n            } else {\r\n                transferArrayItem(\r\n                    event.previousContainer.data,\r\n                    event.container.data,\r\n                    event.previousIndex,\r\n                    event.currentIndex\r\n                )\r\n\r\n                this.updateLocationMatch(targetContainerId,\r\n                    {\r\n                        new_index: this.mapNewSlotIndex(targetContainerId),\r\n                        location_id: targetLocationId,\r\n                        prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\r\n                        stage_id: targetStageId,\r\n                        prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\r\n                        tournament_id: this.tournamentId,\r\n                        config_id: this.getConfigId(targetLocationId),\r\n                        prev_config_id: this.getConfigId(prevLocationId),\r\n                    },\r\n                    () => {\r\n                        transferArrayItem(\r\n                            event.container.data,\r\n                            event.previousContainer.data,\r\n                            event.currentIndex,\r\n                            event.previousIndex\r\n                        );\r\n                        this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                    }, () => {\r\n                        this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                    });\r\n            }\r\n        }\r\n    }\r\n\r\n    getShortTime(time: string): string {\r\n        // handle return ISO string to short time format HH:mm and format 24 hours\r\n        if (!time) return '';\r\n        const date = new Date(time);\r\n        const hours = date.getHours().toString().padStart(2, '0');\r\n        const minutes = date.getMinutes().toString().padStart(2, '0');\r\n        return `${hours}:${minutes}`;\r\n    }\r\n\r\n    getConfigId(location_id) {\r\n        return this.responseMetadata['configs'].find((item) => {\r\n            return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\r\n        })?.id;\r\n    }\r\n\r\n    editPlan(locationId, configId) {\r\n        this.selectedConfig = {\r\n            tournamentId: this.tournamentId,\r\n            location: locationId,\r\n            date: this.selectedDate,\r\n            configId,\r\n        };\r\n        this._modalService.open(this.modalEditSchedule, {\r\n            centered: true,\r\n        });\r\n    }\r\n\r\n    deletePlan(locationKey) {\r\n\r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        const configId = this.getConfigId(locationId);\r\n        const timeSlotIds = this.listMatches[locationKey].map((item) => item.time_slot_id);\r\n\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('You will not be able to recover this schedule!'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe((res) => {\r\n                    this.onScheduleAction(null, false);\r\n                    this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    public breakModalParams: BreakModalParams = {\r\n        locationId: null,\r\n        tournamentId: null,\r\n        timeSlotId: null,\r\n        lastTimeSlotId: null,\r\n        configId: null\r\n    }\r\n\r\n    openModalAddBreak(locationId, lastTimeSlotId, configId) {\r\n        this.breakModalParams = {\r\n            ...this.breakModalParams,\r\n            locationId,\r\n            tournamentId: this.tournamentId,\r\n            lastTimeSlotId,\r\n            configId\r\n        }\r\n\r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true,\r\n\r\n        });\r\n\r\n    }\r\n\r\n    // Simple dropdown methods\r\n    toggleDropdown(event: MouseEvent, item: any): void {\r\n\r\n        event.preventDefault();\r\n        event.stopPropagation();\r\n\r\n        if (event.target['className'] === 'swap-button') {\r\n            return;\r\n        }\r\n\r\n        const dropdownId = this.getDropdownId(item);\r\n\r\n        // Close dropdown if clicking on the same item\r\n        if (this.activeDropdownId === dropdownId) {\r\n            this.activeDropdownId = null;\r\n        } else {\r\n            // Open new dropdown (close any existing one)\r\n            this.activeDropdownId = dropdownId;\r\n        }\r\n    }\r\n\r\n    isDropdownOpen(item: any): boolean {\r\n        const dropdownId = this.getDropdownId(item);\r\n        return this.activeDropdownId === dropdownId;\r\n    }\r\n\r\n    getDropdownId(item: any): string {\r\n        // Create unique ID for each item\r\n        return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\r\n    }\r\n\r\n    closeAllDropdowns(): void {\r\n        this.activeDropdownId = null;\r\n    }\r\n\r\n    @HostListener('document:click', ['$event'])\r\n    onDocumentClick(event: MouseEvent): void {\r\n        const target = event.target as HTMLElement;\r\n\r\n        // Close dropdown if clicking outside of dropdown or dnd-item\r\n        if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\r\n            this.closeAllDropdowns();\r\n        }\r\n    }\r\n\r\n\r\n    public selectedItem = null;\r\n\r\n    // Dropdown action handlers\r\n    onEditMatch(item: any): void {\r\n        this.closeAllDropdowns();\r\n\r\n        this.selectedItem = item;\r\n\r\n        this._modalService.open(this.modalUpdateMatch, {\r\n            centered: true,\r\n        });\r\n\r\n    }\r\n\r\n    onUnscheduleTimeSlot(item: any, configId): void {\r\n        const description = {\r\n            match: 'This match will be moved to unscheduled matches.',\r\n            break: 'This break will be removed.'\r\n        }\r\n\r\n        const successMessage = {\r\n            match: 'Match unscheduled successfully.',\r\n            break: 'Break removed successfully.'\r\n        }\r\n\r\n        const errorMessage = {\r\n            match: 'Failed to unschedule match.',\r\n            break: 'Failed to remove break.'\r\n        }\r\n\r\n\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(description[item.type]),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this.unScheduleMatch(item.time_slot_id, configId, () => {\r\n                    this._toastService.error(this._translateService.instant(errorMessage[item.type]));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant(successMessage[item.type]));\r\n                });\r\n            }\r\n        });\r\n\r\n        this.closeAllDropdowns();\r\n    }\r\n\r\n    onEditEventTime(item: any, configId: number): void {\r\n\r\n        this.closeAllDropdowns();\r\n\r\n        this.breakModalParams = {\r\n            timeSlotId: item.time_slot_id,\r\n            locationId: item.location_id,\r\n            tournamentId: item.tournament_id,\r\n            description: item.description,\r\n            breakDurations: item.break_durations,\r\n            configId: configId,\r\n        }\r\n\r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true,\r\n        })\r\n\r\n    }\r\n\r\n    clearSchedule() {\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('This action will clear all schedule.'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes, clear it!'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._autoScheduleService.clearSchedule(this.tournamentId).subscribe((res) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Success!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                    this.onScheduleAction(null, false);\r\n                }, (error) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Error!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                })\r\n            }\r\n        });\r\n    }\r\n\r\n    isMatchHasConflict(scheduleMatchId: number, type: 'team' | 'referee' | 'match', itemCheckId?: number) {\r\n        switch (type) {\r\n            case 'team':\r\n                return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\r\n            case 'referee':\r\n                return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\r\n            case 'match':\r\n                return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\r\n        }\r\n    }\r\n\r\n    onClickLock() {\r\n        this.isLock = !this.isLock;\r\n        this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe((res) => {\r\n            this._toastService.success(res.message)\r\n        }, (error) => {\r\n\r\n            this.isLock = !this.isLock;\r\n            this._toastService.error(error.message)\r\n        });\r\n    }\r\n\r\n    swapTeam(matchInfo) {\r\n        this.closeAllDropdowns();\r\n        this._stageService.swapTeams(matchInfo).subscribe((res) => {\r\n            this._toastService.success('Swap teams successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            this._toastService.error(error.message || 'Failed to swap teams.');\r\n        })\r\n    }\r\n\r\n    autoGenerate() {\r\n        this._stageService.autoGenerateMatches(this.leagueOrGroupStageId).subscribe((res) => {\r\n            this._toastService.success('Auto generate matches successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            this._toastService.error(error.warning || error.message || 'Failed to auto generate matches.');\r\n        })\r\n    }\r\n\r\n    protected readonly location = location;\r\n\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"top-header\">\r\n      <div class=\"row mb-1\">\r\n        <!-- ng select season -->\r\n        <div class=\"d-flex flex-column\" style=\"gap: 2px\">\r\n          <label for=\"selectDate\">{{ 'Date' | translate }}</label>\r\n          <ng-select\r\n            id=\"selectDate\"\r\n            [searchable]=\"true\"\r\n            [clearable]=\"false\"\r\n            placeholder=\"{{ 'Select Date' | translate }}\"\r\n            [(ngModel)]=\"selectedDate\"\r\n            (change)=\"onSelectDate($event)\"\r\n            style=\"min-width: 200px\"\r\n          >\r\n            <ng-option *ngFor=\"let date of dateOptions\" [value]=\"date\"\r\n              >{{ date }}\r\n            </ng-option>\r\n          </ng-select>\r\n        </div>\r\n        <div class=\"d-flex align-items-center\" style=\"gap: 8px\">\r\n          <button\r\n            class=\"btn btn-primary\"\r\n            (click)=\"openModalSetupSchedule()\"\r\n            [disabled]=\"isLock\"\r\n          >\r\n            <i data-feather=\"settings\" class=\"mr-25\"></i>\r\n            Add Schedule\r\n          </button>\r\n          <button\r\n            class=\"btn btn-icon\"\r\n            (click)=\"onClickLock()\"\r\n            [ngClass]=\"{\r\n              'btn-outline-primary': !isLock,\r\n              'btn-primary': isLock\r\n            }\"\r\n          >\r\n            <i\r\n              [ngClass]=\"isLock ? 'fa-regular fa-lock' : 'fa-regular fa-unlock'\"\r\n            ></i>\r\n            <!--                        {{ isLock ? 'Unlock' : 'Lock' }}-->\r\n          </button>\r\n          <button\r\n            class=\"btn btn-icon btn-outline-danger\"\r\n            (click)=\"clearSchedule()\"\r\n            [disabled]=\"isLock\"\r\n          >\r\n            <i data-feather=\"trash-2\"></i>\r\n            <!--                        Clear Schedule-->\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div cdkDropListGroup>\r\n      <div class=\"col-9\" id=\"listLocationZone\">\r\n        <div class=\"horizontal-scroll-container\" *ngIf=\"hasPlan\">\r\n          <div class=\"location-columns-wrapper\">\r\n            <ng-container *ngFor=\"let locationKey of listLocationIds\">\r\n              <div class=\"location-column mb-2\">\r\n                <div class=\"bg-white shadow-sm\">\r\n                  <header class=\"location-header\">\r\n                    <div\r\n                      class=\"d-flex align-items-start justify-content-between\"\r\n                    >\r\n                      <p class=\"location-name h4\">\r\n                        {{ locationKey }}\r\n                      </p>\r\n                      <div class=\"\" ngbDropdown *ngIf=\"!isLock\">\r\n                        <button\r\n                          class=\"btn btn-link dropdown-toggle\"\r\n                          ngbDropdownToggle\r\n                        >\r\n                          <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n                        </button>\r\n                        <div\r\n                          ngbDropdownMenu\r\n                          aria-labelledby=\"dropdownMenuButton\"\r\n                        >\r\n                          <a\r\n                            ngbDropdownItem\r\n                            (click)=\"\r\n                              editPlan(\r\n                                responseMetadata['locations'][locationKey].id,\r\n                                getConfigId(\r\n                                  responseMetadata['locations'][locationKey].id\r\n                                )\r\n                              )\r\n                            \"\r\n                          >\r\n                            Edit Plan\r\n                          </a>\r\n                          <a ngbDropdownItem (click)=\"deletePlan(locationKey)\">\r\n                            Delete Plan\r\n                          </a>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <p class=\"location-stage-name\">\r\n                      <!-- {{\r\n                        getShortTime(\r\n                          responseMetadata['locations'][locationKey].begin_time\r\n                        )\r\n                      }} -->\r\n                    </p>\r\n                  </header>\r\n                  <div\r\n                    class=\"dnd-zone\"\r\n                    cdkDropList\r\n                    [cdkDropListData]=\"listMatches[locationKey]\"\r\n                    (cdkDropListDropped)=\"drop($event)\"\r\n                    [id]=\"locationKey\"\r\n                    [data_location_id]=\"\r\n                      responseMetadata['locations'][locationKey].id\r\n                    \"\r\n                  >\r\n                    <div\r\n                      *ngFor=\"let item of listMatches[locationKey]\"\r\n                      class=\"dnd-item location-match-row\"\r\n                      [ngClass]=\"{\r\n                        'conflict-border': isMatchHasConflict(item.id, 'match')\r\n                      }\"\r\n                      cdkDrag\r\n                      [data_stage_id]=\"item.stage_id\"\r\n                      [data_time_slot_id]=\"item.time_slot_id\"\r\n                      [data_type]=\"item.type\"\r\n                      [cdkDragDisabled]=\"isLock\"\r\n                      (click)=\"!isLock && toggleDropdown($event, item)\"\r\n                    >\r\n                      <button\r\n                        type=\"button\"\r\n                        rippleEffect\r\n                        class=\"conflict-tooltip btn btn-link\"\r\n                        placement=\"right\"\r\n                        container=\"body\"\r\n                        ngbTooltip=\"This match has conflict\"\r\n                        *ngIf=\"isMatchHasConflict(item.id, 'match')\"\r\n                      >\r\n                        <i\r\n                          class=\"fa-light fa-circle-exclamation\"\r\n                          style=\"font-size: 16px\"\r\n                        ></i>\r\n                      </button>\r\n\r\n                      <ng-container *ngIf=\"item.type === 'match'\">\r\n                        <ng-container\r\n                          *ngTemplateOutlet=\"\r\n                            matchScheduledTemplate;\r\n                            context: { $implicit: item }\r\n                          \"\r\n                        ></ng-container>\r\n                      </ng-container>\r\n                      <ng-container *ngIf=\"item.type === 'break'\">\r\n                        <div class=\"break-info-header\">\r\n                          <p class=\"text-center m-0\">\r\n                            {{ item.description || 'Break time' }}\r\n                          </p>\r\n                        </div>\r\n                        <div class=\"break-row\">\r\n                          <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n                          <p class=\"break-time m-0\">\r\n                            {{ item.break_durations }} mins\r\n                          </p>\r\n                        </div>\r\n                      </ng-container>\r\n\r\n                      <!-- Individual Dropdown for this item -->\r\n                      <div\r\n                        class=\"item-dropdown\"\r\n                        [class.visible]=\"isDropdownOpen(item)\"\r\n                      >\r\n                        <div class=\"dropdown-content\">\r\n                          <!-- Match type dropdown options -->\r\n                          <ng-container *ngIf=\"item.type === 'match'\">\r\n                            <button\r\n                              class=\"dropdown-item\"\r\n                              (click)=\"onEditMatch(item)\"\r\n                            >\r\n                              <i\r\n                                class=\"fa-regular fa-whistle mr-2\"\r\n                                style=\"rotate: -45deg\"\r\n                              ></i>\r\n                              {{ 'Update Match Referees' | translate }}\r\n                            </button>\r\n                            <button\r\n                              class=\"dropdown-item\"\r\n                              (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfigId(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )\r\n                                )\r\n                              \"\r\n                            >\r\n                              <i class=\"fa fa-trash mr-2\"></i>\r\n                              {{ 'Unschedule Match' | translate }}\r\n                            </button>\r\n                          </ng-container>\r\n\r\n                          <!-- Break type dropdown options -->\r\n                          <ng-container *ngIf=\"item.type === 'break'\">\r\n                            <button\r\n                              class=\"dropdown-item\"\r\n                              (click)=\"\r\n                                onEditEventTime(\r\n                                  item,\r\n                                  getConfigId(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )\r\n                                )\r\n                              \"\r\n                            >\r\n                              <i class=\"fa fa-clock mr-2\"></i>\r\n                              {{ 'Edit Event Time' | translate }}\r\n                            </button>\r\n                            <button\r\n                              class=\"dropdown-item\"\r\n                              (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfigId(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )\r\n                                )\r\n                              \"\r\n                            >\r\n                              <i class=\"fa fa-trash mr-2\"></i>\r\n                              {{ 'Delete Event' | translate }}\r\n                            </button>\r\n                          </ng-container>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <footer class=\"location-footer\" *ngIf=\"!isLock\">\r\n                    <button\r\n                      class=\"btn btn-link w-100\"\r\n                      (click)=\"\r\n                        openModalAddBreak(\r\n                          responseMetadata['locations'][locationKey].id,\r\n                          listMatches[locationKey][\r\n                            listMatches[locationKey].length - 1\r\n                          ]?.time_slot_id,\r\n                          getConfigId(\r\n                            responseMetadata['locations'][locationKey].id\r\n                          )\r\n                        )\r\n                      \"\r\n                    >\r\n                      <i class=\"fa fa-plus\" aria-hidden=\"true\"></i>\r\n                      Add event / break\r\n                    </button>\r\n                  </footer>\r\n                </div>\r\n              </div>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n        <ng-container *ngIf=\"!hasPlan && !isFetching\">\r\n          <ng-container *ngTemplateOutlet=\"noSchedule\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"isFetching\">\r\n          <ng-container *ngTemplateOutlet=\"fetchingState\"></ng-container>\r\n        </ng-container>\r\n      </div>\r\n      <div id=\"listUnScheduleZone\" class=\"col-3\">\r\n        <div class=\"unschedule-container\">\r\n          <div class=\"location-column unplanned-matches-container\">\r\n            <div class=\"location-header\">\r\n              <p class=\"h4\">Not Planned</p>\r\n              <p class=\"small\">\r\n                You can add unscheduled matches to the calendar by drag and drop\r\n                them.\r\n              </p>\r\n            </div>\r\n            <div\r\n              class=\"dnd-zone\"\r\n              cdkDropList\r\n              [cdkDropListData]=\"listUnScheduledMatches\"\r\n              (cdkDropListDropped)=\"drop($event)\"\r\n              id=\"unScheduleZone\"\r\n            >\r\n              <ng-container *ngIf=\"hasMatches\">\r\n                <div\r\n                  *ngFor=\"let item of listUnScheduledMatches\"\r\n                  class=\"dnd-item location-match-row\"\r\n                  cdkDrag\r\n                  [data_stage_id]=\"item.stage_id\"\r\n                  [data_time_slot_id]=\"item.time_slot_id\"\r\n                  [data_type]=\"item.type\"\r\n                  [cdkDragDisabled]=\"isLock\"\r\n                >\r\n                  <ng-container *ngIf=\"item.type === 'match'\">\r\n                    <ng-container\r\n                      *ngTemplateOutlet=\"\r\n                        matchNotScheduledTemplate;\r\n                        context: { $implicit: item }\r\n                      \"\r\n                    ></ng-container>\r\n                  </ng-container>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"!hasMatches\">\r\n                <div id=\"notHaveMatches\">\r\n                  <p class=\"text-center\">\r\n                    No matches found for this tournament.\r\n                  </p>\r\n\r\n                  <button (click)=\"autoGenerate()\" class=\"btn btn-primary\">\r\n                    <i class=\"fa-solid fa-wand-magic-sparkles\"></i>\r\n                    Auto Generate\r\n                  </button>\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n<ng-template #modalSetupSchedule let-modal>\r\n  <app-modal-setup-schedule\r\n    [seasonId]=\"seasonId\"\r\n    [tournamentId]=\"tournamentId\"\r\n    [tournamentInfo]=\"tournamentInfo\"\r\n    (onSubmit)=\"onScheduleAction($event)\"\r\n  />\r\n</ng-template>\r\n<ng-template #modalEditSchedule let-modal>\r\n  <app-modal-update-config\r\n    [selectedConfig]=\"selectedConfig\"\r\n    (onSubmit)=\"onScheduleAction($event)\"\r\n  />\r\n</ng-template>\r\n<ng-template #modalCrudBreak let-modal>\r\n  <app-modal-crud-break\r\n    [breakModalParams]=\"breakModalParams\"\r\n    (onSubmit)=\"onScheduleAction($event)\"\r\n  />\r\n</ng-template>\r\n\r\n<ng-template #modalUpdateMatch let-modal>\r\n  <app-modal-update-match\r\n    [timeSlotInfo]=\"selectedItem\"\r\n    [seasonId]=\"seasonId\"\r\n    (onSubmit)=\"onScheduleAction($event)\"\r\n  />\r\n</ng-template>\r\n\r\n<ng-template #noSchedule>\r\n  <div id=\"noSchedule\">\r\n    <div\r\n      class=\"col d-flex flex-column align-items-center justify-content-center g-2\"\r\n      style=\"height: 500px\"\r\n    >\r\n      <p class=\"h5\">No Plan Created</p>\r\n      <p style=\"color: rgba(168, 170, 174, 1)\" class=\"w-75 text-center\">\r\n        Please enter the necessary information to allow the system to generate\r\n        an accurate schedule based on your requirements.\r\n      </p>\r\n      <button class=\"btn btn-primary\" (click)=\"openModalSetupSchedule()\">\r\n        Setup\r\n      </button>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #fetchingState>\r\n  <div id=\"fetchingState\">\r\n    <div\r\n      class=\"col d-flex flex-column align-items-center justify-content-center g-2\"\r\n      style=\"height: 500px\"\r\n    >\r\n      <p class=\"h5\">Fetching</p>\r\n      <p style=\"color: rgba(168, 170, 174, 1)\" class=\"w-75 text-center\">\r\n        Waiting for getting schedule data...\r\n      </p>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #matchNotScheduledTemplate let-item>\r\n  <div class=\"match-info-header\">\r\n    <p class=\"text-center m-0\">{{ item.match.round_name }}</p>\r\n  </div>\r\n  <div class=\"team-row\">\r\n    <div class=\"home-team\">\r\n      <img\r\n        src=\"https://assets.codepen.io/285131/whufc.svg\"\r\n        alt=\"Home Team Logo\"\r\n        class=\"team-logo\"\r\n      />\r\n      <span class=\"h6 team-name\">{{\r\n        item.match?.home_team?.name ?? 'TBD'\r\n      }}</span>\r\n    </div>\r\n    <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n      <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n    </button>\r\n    <div class=\"away-team\">\r\n      <span class=\"h6 team-name\">{{\r\n        item.match?.away_team?.name ?? 'TBD'\r\n      }}</span>\r\n      <img\r\n        src=\"https://assets.codepen.io/285131/whufc.svg\"\r\n        alt=\"Away Team Logo\"\r\n        class=\"team-logo\"\r\n      />\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #matchScheduledTemplate let-item>\r\n  <div class=\"match-info-header\">\r\n    <p class=\"text-center m-0\">\r\n      {{ item.match.round_name }}\r\n    </p>\r\n  </div>\r\n  <div class=\"team-row\">\r\n    <div class=\"home-team\">\r\n      <img\r\n        src=\"https://assets.codepen.io/285131/whufc.svg\"\r\n        alt=\"Home Team Logo\"\r\n        class=\"team-logo\"\r\n      />\r\n      <span\r\n        class=\"h6 team-name\"\r\n        [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.home_team_id\r\n          )\r\n        }\"\r\n      >\r\n        {{ item.match.home_team?.name ?? 'TBD' }}\r\n      </span>\r\n    </div>\r\n    <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n      <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n    </button>\r\n    <div class=\"away-team\">\r\n      <span\r\n        class=\"h6 team-name\"\r\n        [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.away_team_id\r\n          )\r\n        }\"\r\n      >\r\n        {{ item.match.away_team?.name ?? 'TBD' }}\r\n      </span>\r\n      <img\r\n        src=\"https://assets.codepen.io/285131/whufc.svg\"\r\n        alt=\"Away Team Logo\"\r\n        class=\"team-logo\"\r\n      />\r\n    </div>\r\n  </div>\r\n  <div class=\"match-date\">\r\n    <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n    <p class=\"text-center m-0\">\r\n      {{ getShortTime(item.start_time) }}\r\n    </p>\r\n    <p class=\"text-center m-0\">-</p>\r\n    <p class=\"text-center m-0\">\r\n      {{ getShortTime(item.end_time) }}\r\n    </p>\r\n  </div>\r\n  <div class=\"referees-row\" *ngIf=\"item.referees && item.referees.length > 0\">\r\n    <i class=\"fa-regular fa-whistle\" style=\"rotate: -45deg\"></i>\r\n\r\n    <div\r\n      class=\"referee-names\"\r\n      *ngFor=\"let ref of item.referees; let last = last\"\r\n    >\r\n      <div class=\"referee-name\">\r\n        <span\r\n          *ngIf=\"ref.referee_type == 'user'\"\r\n          [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\"\r\n        >\r\n          {{ ref.user?.first_name }} {{ ref.user?.last_name }}\r\n        </span>\r\n        <span\r\n          *ngIf=\"ref.referee_type == 'freetext'\"\r\n          [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\"\r\n        >\r\n          {{ ref.referee_name }}\r\n        </span>\r\n        <span *ngIf=\"!last\">-</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}