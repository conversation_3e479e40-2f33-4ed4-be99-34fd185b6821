<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Location extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'latitude',
        'longitude',
        'surface',
        'parking',
    ];

    public function matches()
    {
        return $this->hasMany(StageMatch::class);
    }

    public function scheduleTimeSlots()
    {
        return $this->hasMany(ScheduleTimeSlot::class);
    }


}
