<?php

namespace App\Http\Controllers;

use App\Models\ScheduleBreak;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\ScheduleTimeSlot;
use App\Models\ScheduleConfig;

class ScheduleBreakController extends Controller
{
    private $scheduleTimeSlotController;

    function __construct()
    {
        $this->scheduleTimeSlotController = new ScheduleTimeSlotController();
    }

    function addBreak(Request $request)
    {
        try {

            $request->validate([
                'tournament_id' => 'required|integer|exists:tournaments,id',
                'location_id' => 'required|integer|exists:locations,id',
                'break_durations' => 'required|integer|min:1',
                'description' => 'nullable|string|max:255',
                'last_time_slot_id' => 'sometimes|integer|nullable|exists:schedule_time_slots,id',
                'config_id' => 'required|integer|exists:schedule_configs,id'
            ]);


            $isHasLastTimeSlot = $request->get('last_time_slot_id') != null;

            $initStartTime = null;
            $initEndTime = null;

            if ($isHasLastTimeSlot) {
                $lastTimeSlotInfo = ScheduleTimeSlot::find($request->get('last_time_slot_id'));
                $initStartTime = Carbon::parse($lastTimeSlotInfo->start_time, 'UTC')->format('Y-m-d H:i:s');
                $initEndTime = Carbon::parse($lastTimeSlotInfo->end_time, 'UTC')->format('Y-m-d H:i:s');
            } else {
                $configInfo = ScheduleConfig::find($request->get('config_id'));
                $initStartTime = Carbon::parse($configInfo->begin_date->format('Y-m-d') . ' ' . $configInfo->begin_time->format('H:i:s'), 'UTC')->format('Y-m-d H:i:s');
                $initEndTime = Carbon::parse($configInfo->begin_date->format('Y-m-d') . ' ' . $configInfo->begin_time->format('H:i:s'), 'UTC')->addMinutes($request->get('break_durations'))->format('Y-m-d H:i:s');

            }

            // create new time slot with type `break`
            $newTimeSlot = ScheduleTimeSlot::create([
                'tournament_id' => $request->get('tournament_id'),
                'location_id' => $request->get('location_id'),
                'start_time' => $initStartTime,
                'end_time' => $initEndTime,
                'type' => 'break',
                'slot_index' => $isHasLastTimeSlot ? $lastTimeSlotInfo->slot_index + 1 : 0,
            ]);

            Log::info('$newTimeSlot', [$newTimeSlot]);

            ScheduleBreak::create([
                'tournament_id' => $request->get('tournament_id'),
                'location_id' => $request->get('location_id'),
                'time_slot_id' => $newTimeSlot->id,
                'break_durations' => $request->get('break_durations'),
                'description' => $request->get('description', ''),
            ]);


            return response()->json([
                'status' => 'success',
                'message' => 'Break added successfully',
                'data' => $this->scheduleTimeSlotController->regenerateTimeSlots($request->get('tournament_id'), $request->get('location_id'), null, $request->get('config_id'), $request->header('X-Time-Zone'))
            ], 201);

        } catch (Exception $error) {
            Log::info('error in ScheduleBreak ~ addBreak', [$error]);
            return response()->json([
                'status' => 'error',
                'message' => $error->getMessage()
            ], 500);
        }
    }

    function updateBreak(Request $request)
    {
        try {
            $request->validate([
                'time_slot_id' => 'required|integer|exists:schedule_time_slots,id',
                'break_durations' => 'required|integer|min:1',
                'description' => 'nullable|string|max:255',
                'tournament_id' => 'required|integer',
                'location_id' => 'required|integer',
                'config_id' => 'required|integer',
            ]);

            ScheduleBreak::where('time_slot_id', $request->get('time_slot_id'))
                ->update([
                    'break_durations' => $request->get('break_durations'),
                    'description' => $request->get('description', ''),
                ]);

            $this->scheduleTimeSlotController->regenerateTimeSlots($request->get('tournament_id'), $request->get('location_id'), null, $request->get('config_id'), $request->header('X-Time-Zone'));

            return response()->json([
                'status' => 'success',
                'message' => 'Break updated successfully',
            ], 200);

        } catch (Exception $error) {
            Log::info('error in ScheduleBreak ~ addBreak', [$error]);
            return response()->json([
                'status' => 'error',
                'message' => $error->getMessage()
            ], 500);
        }

    }
}
