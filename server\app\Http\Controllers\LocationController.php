<?php

namespace App\Http\Controllers;

use App\DataTables\LocationsDataTableEditor;
use App\Models\Location;
use App\Models\StageMatch;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class LocationController extends Controller
{
    public function all(Request $request)
    {
        $clubs = Location::orderBy('name', 'asc')->get();
        return DataTables::of($clubs)->make(true);
    }

    public function editor(LocationsDataTableEditor $editor)
    {
        try {
            //use UserDataTableEditor to save data
            return $editor->process(request());
        } catch (Exception $e) {
            Log::error('Error in editor: ' . $e->getMessage());
            return response()->json(['message' => 'Error in editor'], 500);
        }
    }

    private function createIcsFile($events)
    {
        $icsContent = "BEGIN:VCALENDAR\r\n";
        $icsContent .= "VERSION:2.0\r\n";
        $icsContent .= "PRODID:-//EZ Active//NONSGML v1.0//EN\r\n";

        foreach ($events as $event) {
            $icsContent .= "BEGIN:VEVENT\r\n";
            $icsContent .= "UID:" . uniqid() . "@yourdomain.com\r\n";
            $icsContent .= "DTSTAMP:" . gmdate('Ymd\THis\Z') . "\r\n";
            $icsContent .= "DTSTART:" . gmdate('Ymd\THis\Z', strtotime($event->start_time)) . "\r\n";
            $icsContent .= "DTEND:" . gmdate('Ymd\THis\Z', strtotime($event->end_time)) . "\r\n";
            // I want to show HKD vs USFA (U8 Champ)
            $icsContent .= "SUMMARY:" . $event->home_team->name . " vs " . $event->away_team->name . " (" . $event->stage->tournament->name . ")" . " \r\n";
            if (isset($event->location) && isset($event->location->name)) {
                $icsContent .= "LOCATION:" . $event->location->name . "\r\n";
            } else {
                $icsContent .= "LOCATION:Unknown Location\r\n";
            }
            $icsContent .= "END:VEVENT\r\n";
        }

        $icsContent .= "END:VCALENDAR\r\n";
        return $icsContent;
    }

    public function getFixtures(Request $request)
    {
        if (
            !$request->filled('tournament_id') || !is_numeric($request->tournament_id) || $request->tournament_id <= 0 ||
            !$request->filled('team_id') || !is_numeric($request->team_id) || $request->team_id <= 0
        ) {
            return response()->json(['error' => 'Please fill in all required fields.'], 400);
        }

        $tournamentId = $request->input('tournament_id');
        $teamId = $request->input('team_id');

        $query = StageMatch::with(['homeTeam', 'awayTeam', 'location'])
            ->whereHas('stage', function ($query) use ($tournamentId) {
                $query->where('tournament_id', $tournamentId);
            })
            ->where(function ($query) use ($teamId) {
                $query->where('home_team_id', $teamId)
                    ->orWhere('away_team_id', $teamId);
            });

        $matches = $query->get();

        if ($matches->isEmpty()) {
            return response()->json(['error' => 'No matching fixtures found.'], 404);
        }

        return DataTables::of($matches)->make(true);
    }

    public function generateIcs(Request $request)
    {
        if (
            !$request->filled('tournament_ids') || !is_array($request->tournament_ids) ||
            !$request->filled('club_ids') || !is_array($request->club_ids) ||
            empty($request->tournament_ids) || empty($request->club_ids)
        ) {
            return response()->json(['error' => 'Please fill in all required fields.'], 400);
        }

        $tournamentIds = $request->input('tournament_ids');
        $clubIds = $request->input('club_ids');

        $events = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'stage.tournament'])
            ->whereHas('stage', function ($query) use ($tournamentIds) {
                $query->whereIn('tournament_id', $tournamentIds);
            })
            ->where(function ($query) use ($clubIds) {
                $query->whereHas('homeTeam.club', function ($query) use ($clubIds) {
                    $query->whereIn('club_id', $clubIds);
                })->orWhereHas('awayTeam.club', function ($query) use ($clubIds) {
                    $query->whereIn('club_id', $clubIds);
                });
            })
            ->whereNull('home_score')
            ->whereNull('away_score')
            ->whereNotNull('location_id')
            ->where(function ($query) {
                $query->whereNull('status')->orWhere('status', '');
            })
            ->where('start_time', '>=', now())
            ->orderBy('start_time', 'asc')
            ->limit(25)
            ->get();

        if ($events->isEmpty()) {
            return response()->json(['error' => 'No matching fixtures found.'], 404);
        }

        $events = json_decode($events);
        Log::info($events);

        // return response()->json($events);
        $icsContent = $this->createIcsFile($events);

        return response($icsContent, 200)
            ->header('Content-Type', 'text/calendar')
            ->header('Content-Disposition', 'attachment; filename="fixtures.ics"');
    }
}
