{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport { forkJoin } from 'rxjs';\nimport moment from \"moment\";\nimport { AppConfig } from \"../../../../app-config\";\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/tournament.service\";\nimport * as i4 from \"../../../../services/location.service\";\nimport * as i5 from \"../../../../services/auto-schedule.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"app/services/season.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i10 from \"@ngx-formly/core\";\nexport class ModalSetupScheduleComponent {\n  constructor(_modalService, _translateService, _tournamentService, _locationService, _autoSchedule, _loadingService, _seasonService) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._tournamentService = _tournamentService;\n    this._locationService = _locationService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this._seasonService = _seasonService;\n    this.setupScheduleForm = new FormGroup({});\n    this.setupScheduleModel = {};\n    this.listStages = [];\n    this.refereeIds = [];\n    this.listGroups = [];\n    this.setupScheduleFields = [{\n      key: 'list_stages',\n      type: 'ng-select',\n      props: {\n        required: true,\n        multiple: false,\n        closeOnSelect: true,\n        label: 'Stage',\n        placeholder: 'Please select a stage to schedule',\n        options: []\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Please select a stage to schedule.')\n        }\n      },\n      hooks: {\n        onChanges: field => {\n          field.form.get('list_stages').valueChanges.subscribe(value => {\n            this.setupScheduleModel['stage_id'] = this.listStages.find(item => item.name === value)?.id || -1;\n          });\n        }\n      }\n    }, {\n      key: 'list_group_names',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: 'Groups',\n        placeholder: 'Select groups to schedule (leave empty to include all)',\n        options: [],\n        hide: true\n      },\n      hooks: {},\n      expressions: {\n        hide: 'model.list_stages !== \"Groups\"'\n      }\n    }, {\n      fieldGroupClassName: 'row',\n      fieldGroup: [{\n        className: 'col-md-4',\n        key: 'begin_date',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Date'),\n          placeholder: this._translateService.instant('Select start date'),\n          required: true,\n          type: 'date'\n        },\n        defaultValue: moment().format('YYYY-MM-DD')\n      }, {\n        className: 'col-md-4',\n        key: 'begin_time',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('First Match Start Time'),\n          placeholder: this._translateService.instant('Select start time'),\n          required: true,\n          type: 'time'\n        },\n        defaultValue: moment().format('HH:mm')\n      }, {\n        className: 'col-md-4',\n        key: 'end_time',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Latest End Time'),\n          placeholder: this._translateService.instant('Select end time'),\n          required: true,\n          type: 'time'\n        },\n        defaultValue: moment().format('HH:mm')\n      }]\n    }, {\n      fieldGroupClassName: 'row',\n      fieldGroup: [{\n        className: 'col-md-6',\n        key: 'match_duration',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Match Duration (minutes)'),\n          placeholder: this._translateService.instant('Enter match duration'),\n          required: true,\n          type: 'number',\n          min: 1,\n          step: 1,\n          pattern: '[0-9]*'\n        },\n        defaultValue: 25,\n        validation: {\n          messages: {\n            min: this._translateService.instant('Match duration must be at least 1 minute.'),\n            pattern: this._translateService.instant('Match duration must be a whole number.')\n          }\n        }\n      }, {\n        className: 'col-md-6',\n        key: 'break_duration',\n        type: 'input',\n        props: {\n          label: this._translateService.instant('Break Duration (minutes)'),\n          placeholder: this._translateService.instant('Enter break duration between matches'),\n          required: true,\n          type: 'number',\n          min: 0,\n          step: 1,\n          pattern: '[0-9]*'\n        },\n        defaultValue: 0,\n        validation: {\n          messages: {\n            min: this._translateService.instant('Break duration must be at least 0 minutes.'),\n            pattern: this._translateService.instant('Break duration must be a whole number.')\n          }\n        }\n      }]\n    }, {\n      key: 'list_location_ids',\n      type: 'ng-select',\n      props: {\n        multiple: true,\n        required: true,\n        hideOnMultiple: true,\n        defaultValue: [],\n        label: 'Locations',\n        placeholder: 'Select locations for scheduling',\n        options: []\n      },\n      hooks: {},\n      validation: {\n        messages: {\n          required: this._translateService.instant('Please select at least one location.')\n        }\n      }\n    }, {\n      fieldGroupClassName: \"row\",\n      fieldGroup: [{\n        key: 'nums_of_referees',\n        type: 'input',\n        className: 'col-md-4',\n        defaultValue: 0,\n        props: {\n          label: this._translateService.instant('Referees per Match'),\n          placeholder: this._translateService.instant('Enter number of referees'),\n          required: true,\n          type: 'number',\n          min: 0\n        },\n        validation: {\n          messages: {\n            min: this._translateService.instant('Number of referees must be at least 0.')\n          }\n        }\n      }, {\n        key: 'list_referee_ids',\n        type: 'ng-select',\n        className: 'col-md-8',\n        props: {\n          multiple: true,\n          hideOnMultiple: true,\n          defaultValue: [],\n          label: this._translateService.instant('Referees'),\n          placeholder: this._translateService.instant('Select referees'),\n          options: []\n        },\n        expressions: {\n          \"props.disabled\": 'model.nums_of_referees === 0',\n          \"props.required\": 'model.nums_of_referees > 0'\n        },\n        validation: {\n          messages: {\n            required: this._translateService.instant('Please select at least one referee.')\n          }\n        }\n      }]\n    }, {\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'stage_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {\n    this._loadingService.show();\n    this.setupScheduleFields[this.setupScheduleFields.length - 2].defaultValue = this.tournamentId;\n    const observables = [this._tournamentService.getGroupInTournament(this.tournamentId), this._locationService.getAllLocations(), this._seasonService.getListSeasonReferees(this.seasonId), this._tournamentService.getStagesInTournament(this.tournamentId)];\n    forkJoin(observables).subscribe({\n      next: ([groupRes, locationRes, refereeRes, stagesRes]) => {\n        this.listGroups = groupRes.data;\n        this.setupScheduleFields[1].props.options = groupRes.data;\n        this.setupScheduleFields[4].props.options = locationRes['data'].map(location => ({\n          label: location.name,\n          value: location.id\n        }));\n        this.setupScheduleFields[5].fieldGroup[1].props.options = refereeRes['data'].map(referee => {\n          this.refereeIds.push(referee.id);\n          return {\n            label: referee.user ? `${referee.user.first_name} ${referee.user.last_name}` : referee.referee_name,\n            value: referee.id\n          };\n        });\n        this.listStages = stagesRes;\n        if (this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\n          this.setupScheduleFields[0].props.options = [{\n            label: 'All',\n            value: 'All'\n          }, ...stagesRes.map(item => ({\n            label: item.name,\n            value: item.name\n          }))];\n        } else {\n          this.setupScheduleFields[0].props.options = [...stagesRes.map(item => ({\n            label: item.name,\n            value: item.name\n          }))];\n        }\n      },\n      complete: () => {\n        this._loadingService.dismiss();\n      }\n    });\n  }\n  onSubmitSetup(model) {\n    // handle check model is all valids\n    if (this.setupScheduleForm.invalid) {\n      return;\n    }\n    if (model.list_stages === AppConfig.TOURNAMENT_TYPES.groups && !model.list_group_names) {\n      model.list_group_names = this.listGroups;\n    }\n    this._autoSchedule.scheduleTournament(model).subscribe(res => {\n      console.log('res', res);\n      this.onSubmit.emit(res);\n      this._modalService.dismissAll();\n    }, error => {\n      Swal.fire({\n        title: 'Cannot Auto Schedule!',\n        text: error.message,\n        icon: 'warning',\n        confirmButtonText: 'Ok'\n      });\n    });\n  }\n  closeModal() {\n    this.setupScheduleModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.setupScheduleForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalSetupScheduleComponent_Factory(t) {\n    return new (t || ModalSetupScheduleComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.TournamentService), i0.ɵɵdirectiveInject(i4.LocationService), i0.ɵɵdirectiveInject(i5.AutoScheduleService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.SeasonService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalSetupScheduleComponent,\n    selectors: [[\"app-modal-setup-schedule\"]],\n    inputs: {\n      tournamentId: \"tournamentId\",\n      tournamentInfo: \"tournamentInfo\",\n      seasonId: \"seasonId\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"modalSetupSchedule\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalSetupScheduleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalSetupScheduleComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalSetupScheduleComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitSetup(ctx.setupScheduleModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalSetupScheduleComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitSetup(ctx.setupScheduleModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Setup Schedule\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.setupScheduleForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.setupScheduleForm)(\"fields\", ctx.setupScheduleFields)(\"model\", ctx.setupScheduleModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.setupScheduleForm.valid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Plan Match\"), \" \");\n      }\n    },\n    dependencies: [i8.ɵNgNoValidate, i8.NgControlStatusGroup, i8.FormGroupDirective, i9.RippleEffectDirective, i10.FormlyForm, i2.TranslatePipe],\n    styles: [\".btn-clear[_ngcontent-%COMP%] {\\n  color: rgb(234, 84, 85);\\n}\\n\\n.w-max[_ngcontent-%COMP%] {\\n  width: max-content;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvYXV0by1zY2hlZHVsZS9tb2RhbC1zZXR1cC1zY2hlZHVsZS9tb2RhbC1zZXR1cC1zY2hlZHVsZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHVCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmJ0bi1jbGVhciB7XHJcbiAgY29sb3I6IHJnYmEoMjM0LCA4NCwgODUsIDEpO1xyXG59XHJcblxyXG4udy1tYXgge1xyXG4gIHdpZHRoOiBtYXgtY29udGVudDtcclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGdhcDogLjI1cmVtO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AAMtE,SAASC,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,QAAQ,QAAQ,MAAM;AAE/B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;AAO9B,OAAM,MAAOC,2BAA2B;EAoOpCC,YACYC,aAAuB,EACvBC,iBAAmC,EACnCC,kBAAqC,EACrCC,gBAAiC,EACjCC,aAAkC,EAClCC,eAA+B,EAC/BC,cAA6B;IAN7B,kBAAa,GAAbN,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,uBAAkB,GAAlBC,kBAAkB;IAClB,qBAAgB,GAAhBC,gBAAgB;IAChB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,mBAAc,GAAdC,cAAc;IApO1B,sBAAiB,GAAG,IAAIb,SAAS,CAAC,EAAE,CAAC;IACrC,uBAAkB,GAAG,EAAE;IAEvB,eAAU,GAAG,EAAE;IACf,eAAU,GAAG,EAAE;IACf,eAAU,GAAG,EAAE;IAER,wBAAmB,GAAwB,CAC9C;MACIc,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,IAAI;QACnBC,KAAK,EAAE,OAAO;QACdC,WAAW,EAAE,mCAAmC;QAChDC,OAAO,EAAE;OACZ;MACDC,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNP,QAAQ,EAAE,IAAI,CAACT,iBAAiB,CAACiB,OAAO,CAAC,oCAAoC;;OAEpF;MACDC,KAAK,EAAE;QACHC,SAAS,EAAGC,KAAK,IAAI;UACjBA,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,aAAa,CAAC,CAACC,YAAY,CAACC,SAAS,CAACC,KAAK,IAAG;YACzD,IAAI,CAACC,kBAAkB,CAAC,UAAU,CAAC,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKL,KAAK,CAAC,EAAEM,EAAE,IAAI,CAAC,CAAC;UACrG,CAAC,CAAC;QACN;;KAEP,EACD;MACIzB,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHE,QAAQ,EAAE,IAAI;QACdsB,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBrB,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,wDAAwD;QACrEC,OAAO,EAAE,EAAE;QACXoB,IAAI,EAAE;OACT;MACDhB,KAAK,EAAE,EAAE;MACTiB,WAAW,EAAE;QACTD,IAAI,EAAE;;KAEb,EACD;MACIE,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CACR;QACIC,SAAS,EAAE,UAAU;QACrBhC,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,MAAM,CAAC;UAC7CJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,mBAAmB,CAAC;UAChER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACD0B,YAAY,EAAEvC,MAAM,EAAE,CAAC6C,MAAM,CAAC,YAAY;OAC7C,EACD;QACID,SAAS,EAAE,UAAU;QACrBhC,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,wBAAwB,CAAC;UAC/DJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,mBAAmB,CAAC;UAChER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACD0B,YAAY,EAAEvC,MAAM,EAAE,CAAC6C,MAAM,CAAC,OAAO;OACxC,EACD;QACID,SAAS,EAAE,UAAU;QACrBhC,GAAG,EAAE,UAAU;QACfC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,iBAAiB,CAAC;UACxDJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,iBAAiB,CAAC;UAC9DR,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE;SACT;QACD0B,YAAY,EAAEvC,MAAM,EAAE,CAAC6C,MAAM,CAAC,OAAO;OACxC;KAER,EACD;MACIH,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CAER;QACIC,SAAS,EAAE,UAAU;QACrBhC,GAAG,EAAE,gBAAgB;QACrBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,0BAA0B,CAAC;UACjEJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,sBAAsB,CAAC;UACnER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACdiC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE;SACZ;QACDT,YAAY,EAAE,EAAE;QAChBlB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNwB,GAAG,EAAE,IAAI,CAACxC,iBAAiB,CAACiB,OAAO,CAAC,2CAA2C,CAAC;YAChFyB,OAAO,EAAE,IAAI,CAAC1C,iBAAiB,CAACiB,OAAO,CAAC,wCAAwC;;;OAG3F,EACD;QACIqB,SAAS,EAAE,UAAU;QACrBhC,GAAG,EAAE,gBAAgB;QACrBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,0BAA0B,CAAC;UACjEJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,sCAAsC,CAAC;UACnFR,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACdiC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE;SACZ;QACDT,YAAY,EAAE,CAAC;QACflB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNwB,GAAG,EAAE,IAAI,CAACxC,iBAAiB,CAACiB,OAAO,CAAC,4CAA4C,CAAC;YACjFyB,OAAO,EAAE,IAAI,CAAC1C,iBAAiB,CAACiB,OAAO,CAAC,wCAAwC;;;OAG3F;KAER,EACD;MACIX,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE;QACHE,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAE,IAAI;QACduB,cAAc,EAAE,IAAI;QACpBC,YAAY,EAAE,EAAE;QAChBrB,KAAK,EAAE,WAAW;QAClBC,WAAW,EAAE,iCAAiC;QAC9CC,OAAO,EAAE;OACZ;MACDI,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNP,QAAQ,EAAE,IAAI,CAACT,iBAAiB,CAACiB,OAAO,CAAC,sCAAsC;;;KAG1F,EACD;MACImB,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CACR;QACI/B,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,OAAO;QACb+B,SAAS,EAAE,UAAU;QACrBL,YAAY,EAAE,CAAC;QACfzB,KAAK,EAAE;UACHI,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,oBAAoB,CAAC;UAC3DJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,0BAA0B,CAAC;UACvER,QAAQ,EAAE,IAAI;UACdF,IAAI,EAAE,QAAQ;UACdiC,GAAG,EAAE;SACR;QACDzB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNwB,GAAG,EAAE,IAAI,CAACxC,iBAAiB,CAACiB,OAAO,CAAC,wCAAwC;;;OAGvF,EACD;QACIX,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,WAAW;QACjB+B,SAAS,EAAE,UAAU;QACrB9B,KAAK,EAAE;UACHE,QAAQ,EAAE,IAAI;UACdsB,cAAc,EAAE,IAAI;UACpBC,YAAY,EAAE,EAAE;UAChBrB,KAAK,EAAE,IAAI,CAACZ,iBAAiB,CAACiB,OAAO,CAAC,UAAU,CAAC;UACjDJ,WAAW,EAAE,IAAI,CAACb,iBAAiB,CAACiB,OAAO,CAAC,iBAAiB,CAAC;UAC9DH,OAAO,EAAE;SACZ;QACDqB,WAAW,EAAE;UACT,gBAAgB,EAAE,8BAA8B;UAChD,gBAAgB,EAAE;SACrB;QACDpB,UAAU,EAAE;UACRC,QAAQ,EAAE;YACNP,QAAQ,EAAE,IAAI,CAACT,iBAAiB,CAACiB,OAAO,CAAC,qCAAqC;;;OAIzF;KAER,EACD;MACIX,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,CACJ;IAES,aAAQ,GAAG,IAAIhB,YAAY,EAAE;EAYvC;EAEAoD,QAAQ;IACJ,IAAI,CAACvC,eAAe,CAACwC,IAAI,EAAE;IAE3B,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACA,mBAAmB,CAACC,MAAM,GAAG,CAAC,CAAC,CAACb,YAAY,GAAG,IAAI,CAACc,YAAY;IAE9F,MAAMC,WAAW,GAAG,CAChB,IAAI,CAAC/C,kBAAkB,CAACgD,oBAAoB,CAAC,IAAI,CAACF,YAAY,CAAC,EAC/D,IAAI,CAAC7C,gBAAgB,CAACgD,eAAe,EAAE,EACvC,IAAI,CAAC7C,cAAc,CAAC8C,qBAAqB,CAAC,IAAI,CAACC,QAAQ,CAAC,EACxD,IAAI,CAACnD,kBAAkB,CAACoD,qBAAqB,CAAC,IAAI,CAACN,YAAY,CAAC,CACnE;IAEDtD,QAAQ,CAACuD,WAAW,CAAC,CAACxB,SAAS,CAAC;MAC5B8B,IAAI,EAAE,CAAC,CAACC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,CAAC,KAAI;QACrD,IAAI,CAACC,UAAU,GAAGJ,QAAQ,CAACK,IAAI;QAC/B,IAAI,CAACf,mBAAmB,CAAC,CAAC,CAAC,CAACrC,KAAK,CAACM,OAAO,GAAGyC,QAAQ,CAACK,IAAI;QAEzD,IAAI,CAACf,mBAAmB,CAAC,CAAC,CAAC,CAACrC,KAAK,CAACM,OAAO,GAAG0C,WAAW,CAAC,MAAM,CAAC,CAACK,GAAG,CAAEC,QAAQ,KAAM;UAC/ElD,KAAK,EAAEkD,QAAQ,CAAChC,IAAI;UACpBL,KAAK,EAAEqC,QAAQ,CAAC/B;SACnB,CAAC,CAAC;QAEH,IAAI,CAACc,mBAAmB,CAAC,CAAC,CAAC,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC7B,KAAK,CAACM,OAAO,GAAG2C,UAAU,CAAC,MAAM,CAAC,CAACI,GAAG,CAAEE,OAAO,IAAI;UACzF,IAAI,CAACC,UAAU,CAACC,IAAI,CAACF,OAAO,CAAChC,EAAE,CAAC;UAChC,OAAO;YACHnB,KAAK,EAAEmD,OAAO,CAACG,IAAI,GACb,GAAGH,OAAO,CAACG,IAAI,CAACC,UAAU,IAAIJ,OAAO,CAACG,IAAI,CAACE,SAAS,EAAE,GACtDL,OAAO,CAACM,YAAY;YAC1B5C,KAAK,EAAEsC,OAAO,CAAChC;WAClB;QACL,CAAC,CAAC;QAEF,IAAI,CAACJ,UAAU,GAAG+B,SAAS;QAE3B,IAAI,IAAI,CAACY,cAAc,CAAC,MAAM,CAAC,KAAK3E,SAAS,CAAC4E,gBAAgB,CAACC,gBAAgB,EAAE;UAC7E,IAAI,CAAC3B,mBAAmB,CAAC,CAAC,CAAC,CAACrC,KAAK,CAACM,OAAO,GAAG,CACxC;YACIF,KAAK,EAAE,KAAK;YACZa,KAAK,EAAE;WACV,EACD,GAAGiC,SAAS,CAACG,GAAG,CAAEhC,IAAI,KAAM;YACxBjB,KAAK,EAAEiB,IAAI,CAACC,IAAI;YAChBL,KAAK,EAAEI,IAAI,CAACC;WACf,CAAC,CAAC,CACN;SACJ,MAAM;UACH,IAAI,CAACe,mBAAmB,CAAC,CAAC,CAAC,CAACrC,KAAK,CAACM,OAAO,GAAG,CACxC,GAAG4C,SAAS,CAACG,GAAG,CAAEhC,IAAI,KAAM;YACxBjB,KAAK,EAAEiB,IAAI,CAACC,IAAI;YAChBL,KAAK,EAAEI,IAAI,CAACC;WACf,CAAC,CAAC,CACN;;MAET,CAAC;MACD2C,QAAQ,EAAE,MAAK;QACX,IAAI,CAACrE,eAAe,CAACsE,OAAO,EAAE;MAClC;KACH,CAAC;EACN;EAEAC,aAAa,CAACC,KAAK;IACf;IACA,IAAI,IAAI,CAACC,iBAAiB,CAACC,OAAO,EAAE;MAChC;;IAGJ,IAAIF,KAAK,CAACG,WAAW,KAAKpF,SAAS,CAAC4E,gBAAgB,CAACS,MAAM,IAAI,CAACJ,KAAK,CAACK,gBAAgB,EAAE;MACpFL,KAAK,CAACK,gBAAgB,GAAG,IAAI,CAACtB,UAAU;;IAG5C,IAAI,CAACxD,aAAa,CAAC+E,kBAAkB,CAACN,KAAK,CAAC,CAACpD,SAAS,CAAE2D,GAAG,IAAI;MAC3DC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEF,GAAG,CAAC;MACvB,IAAI,CAACG,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC;MACvB,IAAI,CAACpF,aAAa,CAACyF,UAAU,EAAE;IACnC,CAAC,EAAGC,KAAK,IAAI;MACT7F,IAAI,CAAC8F,IAAI,CAAC;QACNC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACtB,CAAC;IACN,CAAC,CAAC;EACN;EAGAC,UAAU;IACN,IAAI,CAACtE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC3B,aAAa,CAACyF,UAAU,EAAE;EACnC;EAEAS,SAAS;IACL,IAAI,CAACpB,iBAAiB,CAACqB,KAAK,EAAE;EAClC;EAAC;qBA5UQrG,2BAA2B;EAAA;EAAA;UAA3BA,2BAA2B;IAAAsG;IAAAC;MAAArD;MAAAuB;MAAAlB;IAAA;IAAAiD;MAAAf;IAAA;IAAAgB;IAAAC;IAAAC;IAAAC;MAAA;QCpBxCC,8BAA0B;QACwBA,YAAkC;;QAAAA,iBAAK;QACvFA,iCAKC;QAFCA;UAAA,OAASC,gBAAY;QAAA,EAAC;QAGtBD,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,+BAGC;QADCA;UAAA,OAAYC,yCAAiC;QAAA,EAAC;QAE9CD,8BAAkD;QAK9CA;UAAA,OAAUC,yCAAiC;QAAA,EAAC;QAC7CD,iBAAc;QAEjBA,+BAA0B;QAItBA,aACF;;QAAAA,iBAAS;;;QA3BqCA,eAAkC;QAAlCA,4DAAkC;QAWlFA,eAA+B;QAA/BA,iDAA+B;QAK3BA,eAA0B;QAA1BA,4CAA0B;QAQpBA,eAAqC;QAArCA,uDAAqC;QAE3CA,eACF;QADEA,oEACF", "names": ["EventEmitter", "FormGroup", "fork<PERSON><PERSON>n", "moment", "AppConfig", "<PERSON><PERSON>", "ModalSetupScheduleComponent", "constructor", "_modalService", "_translateService", "_tournamentService", "_locationService", "_autoSchedule", "_loadingService", "_seasonService", "key", "type", "props", "required", "multiple", "closeOnSelect", "label", "placeholder", "options", "validation", "messages", "instant", "hooks", "onChanges", "field", "form", "get", "valueChanges", "subscribe", "value", "setupScheduleModel", "listStages", "find", "item", "name", "id", "hideOnMultiple", "defaultValue", "hide", "expressions", "fieldGroupClassName", "fieldGroup", "className", "format", "min", "step", "pattern", "ngOnInit", "show", "setupScheduleFields", "length", "tournamentId", "observables", "getGroupInTournament", "getAllLocations", "getListSeasonReferees", "seasonId", "getStagesInTournament", "next", "groupRes", "locationRes", "referee<PERSON>es", "stagesRes", "listGroups", "data", "map", "location", "referee", "refereeIds", "push", "user", "first_name", "last_name", "referee_name", "tournamentInfo", "TOURNAMENT_TYPES", "groups_knockouts", "complete", "dismiss", "onSubmitSetup", "model", "setupScheduleForm", "invalid", "list_stages", "groups", "list_group_names", "scheduleTournament", "res", "console", "log", "onSubmit", "emit", "dismissAll", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "closeModal", "clearForm", "reset", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-setup-schedule\\modal-setup-schedule.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-setup-schedule\\modal-setup-schedule.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { TournamentService } from '../../../../services/tournament.service';\r\nimport { LocationService } from '../../../../services/location.service';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { AutoScheduleService } from '../../../../services/auto-schedule.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { forkJoin } from 'rxjs';\r\nimport { SeasonService } from 'app/services/season.service';\r\nimport moment from \"moment\";\r\nimport { AppConfig } from \"../../../../app-config\";\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n    selector: 'app-modal-setup-schedule',\r\n    templateUrl: './modal-setup-schedule.component.html',\r\n    styleUrls: ['./modal-setup-schedule.component.scss']\r\n})\r\nexport class ModalSetupScheduleComponent {\r\n\r\n    @Input() tournamentId: string;\r\n    @Input() tournamentInfo: string;\r\n    @Input() seasonId: string;\r\n\r\n\r\n    setupScheduleForm = new FormGroup({});\r\n    setupScheduleModel = {};\r\n\r\n    listStages = [];\r\n    refereeIds = [];\r\n    listGroups = [];\r\n\r\n    public setupScheduleFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'list_stages',\r\n            type: 'ng-select',\r\n            props: {\r\n                required: true,\r\n                multiple: false,\r\n                closeOnSelect: true,\r\n                label: 'Stage',\r\n                placeholder: 'Please select a stage to schedule',\r\n                options: []\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Please select a stage to schedule.')\r\n                }\r\n            },\r\n            hooks: {\r\n                onChanges: (field) => {\r\n                    field.form.get('list_stages').valueChanges.subscribe(value => {\r\n                        this.setupScheduleModel['stage_id'] = this.listStages.find(item => item.name === value)?.id || -1;\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'list_group_names',\r\n            type: 'ng-select',\r\n            props: {\r\n                multiple: true,\r\n                hideOnMultiple: true,\r\n                defaultValue: [],\r\n                label: 'Groups',\r\n                placeholder: 'Select groups to schedule (leave empty to include all)',\r\n                options: [],\r\n                hide: true\r\n            },\r\n            hooks: {},\r\n            expressions: {\r\n                hide: 'model.list_stages !== \"Groups\"',\r\n            }\r\n        },\r\n        {\r\n            fieldGroupClassName: 'row',\r\n            fieldGroup: [\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'begin_date',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Date'),\r\n                        placeholder: this._translateService.instant('Select start date'),\r\n                        required: true,\r\n                        type: 'date'\r\n                    },\r\n                    defaultValue: moment().format('YYYY-MM-DD')\r\n                },\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'begin_time',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('First Match Start Time'),\r\n                        placeholder: this._translateService.instant('Select start time'),\r\n                        required: true,\r\n                        type: 'time'\r\n                    },\r\n                    defaultValue: moment().format('HH:mm')\r\n                },\r\n                {\r\n                    className: 'col-md-4',\r\n                    key: 'end_time',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Latest End Time'),\r\n                        placeholder: this._translateService.instant('Select end time'),\r\n                        required: true,\r\n                        type: 'time'\r\n                    },\r\n                    defaultValue: moment().format('HH:mm')\r\n                },\r\n            ]\r\n        },\r\n        {\r\n            fieldGroupClassName: 'row',\r\n            fieldGroup: [\r\n\r\n                {\r\n                    className: 'col-md-6',\r\n                    key: 'match_duration',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Match Duration (minutes)'),\r\n                        placeholder: this._translateService.instant('Enter match duration'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 1,\r\n                        step: 1,\r\n                        pattern: '[0-9]*'\r\n                    },\r\n                    defaultValue: 25,\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Match duration must be at least 1 minute.'),\r\n                            pattern: this._translateService.instant('Match duration must be a whole number.')\r\n                        }\r\n                    }\r\n                },\r\n                {\r\n                    className: 'col-md-6',\r\n                    key: 'break_duration',\r\n                    type: 'input',\r\n                    props: {\r\n                        label: this._translateService.instant('Break Duration (minutes)'),\r\n                        placeholder: this._translateService.instant('Enter break duration between matches'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 0,\r\n                        step: 1,\r\n                        pattern: '[0-9]*'\r\n                    },\r\n                    defaultValue: 0,\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Break duration must be at least 0 minutes.'),\r\n                            pattern: this._translateService.instant('Break duration must be a whole number.')\r\n                        }\r\n                    }\r\n                },\r\n            ]\r\n        },\r\n        {\r\n            key: 'list_location_ids',\r\n            type: 'ng-select',\r\n            props: {\r\n                multiple: true,\r\n                required: true,\r\n                hideOnMultiple: true,\r\n                defaultValue: [],\r\n                label: 'Locations',\r\n                placeholder: 'Select locations for scheduling',\r\n                options: []\r\n            },\r\n            hooks: {},\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Please select at least one location.')\r\n                }\r\n            },\r\n        },\r\n        {\r\n            fieldGroupClassName: \"row\",\r\n            fieldGroup: [\r\n                {\r\n                    key: 'nums_of_referees',\r\n                    type: 'input',\r\n                    className: 'col-md-4',\r\n                    defaultValue: 0,\r\n                    props: {\r\n                        label: this._translateService.instant('Referees per Match'),\r\n                        placeholder: this._translateService.instant('Enter number of referees'),\r\n                        required: true,\r\n                        type: 'number',\r\n                        min: 0,\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            min: this._translateService.instant('Number of referees must be at least 0.')\r\n                        }\r\n                    }\r\n                },\r\n                {\r\n                    key: 'list_referee_ids',\r\n                    type: 'ng-select',\r\n                    className: 'col-md-8',\r\n                    props: {\r\n                        multiple: true,\r\n                        hideOnMultiple: true,\r\n                        defaultValue: [],\r\n                        label: this._translateService.instant('Referees'),\r\n                        placeholder: this._translateService.instant('Select referees'),\r\n                        options: []\r\n                    },\r\n                    expressions: {\r\n                        \"props.disabled\": 'model.nums_of_referees === 0',\r\n                        \"props.required\": 'model.nums_of_referees > 0'\r\n                    },\r\n                    validation: {\r\n                        messages: {\r\n                            required: this._translateService.instant('Please select at least one referee.')\r\n                        }\r\n                    }\r\n\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            key: 'tournament_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'stage_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        }\r\n    ];\r\n\r\n    @Output() onSubmit = new EventEmitter();\r\n\r\n    constructor(\r\n        private _modalService: NgbModal,\r\n        private _translateService: TranslateService,\r\n        private _tournamentService: TournamentService,\r\n        private _locationService: LocationService,\r\n        private _autoSchedule: AutoScheduleService,\r\n        private _loadingService: LoadingService,\r\n        private _seasonService: SeasonService,\r\n    ) {\r\n\r\n    }\r\n\r\n    ngOnInit() {\r\n        this._loadingService.show();\r\n\r\n        this.setupScheduleFields[this.setupScheduleFields.length - 2].defaultValue = this.tournamentId;\r\n\r\n        const observables = [\r\n            this._tournamentService.getGroupInTournament(this.tournamentId),\r\n            this._locationService.getAllLocations(),\r\n            this._seasonService.getListSeasonReferees(this.seasonId),\r\n            this._tournamentService.getStagesInTournament(this.tournamentId)\r\n        ];\r\n\r\n        forkJoin(observables).subscribe({\r\n            next: ([groupRes, locationRes, refereeRes, stagesRes]) => {\r\n                this.listGroups = groupRes.data;\r\n                this.setupScheduleFields[1].props.options = groupRes.data;\r\n\r\n                this.setupScheduleFields[4].props.options = locationRes['data'].map((location) => ({\r\n                    label: location.name,\r\n                    value: location.id\r\n                }));\r\n\r\n                this.setupScheduleFields[5].fieldGroup[1].props.options = refereeRes['data'].map((referee) => {\r\n                    this.refereeIds.push(referee.id);\r\n                    return {\r\n                        label: referee.user\r\n                            ? `${referee.user.first_name} ${referee.user.last_name}`\r\n                            : referee.referee_name,\r\n                        value: referee.id\r\n                    }\r\n                });\r\n\r\n                this.listStages = stagesRes;\r\n\r\n                if (this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\r\n                    this.setupScheduleFields[0].props.options = [\r\n                        {\r\n                            label: 'All',\r\n                            value: 'All',\r\n                        },\r\n                        ...stagesRes.map((item) => ({\r\n                            label: item.name,\r\n                            value: item.name\r\n                        }))\r\n                    ];\r\n                } else {\r\n                    this.setupScheduleFields[0].props.options = [\r\n                        ...stagesRes.map((item) => ({\r\n                            label: item.name,\r\n                            value: item.name\r\n                        }))\r\n                    ];\r\n                }\r\n            },\r\n            complete: () => {\r\n                this._loadingService.dismiss();\r\n            }\r\n        });\r\n    }\r\n\r\n    onSubmitSetup(model) {\r\n        // handle check model is all valids\r\n        if (this.setupScheduleForm.invalid) {\r\n            return;\r\n        }\r\n\r\n        if (model.list_stages === AppConfig.TOURNAMENT_TYPES.groups && !model.list_group_names) {\r\n            model.list_group_names = this.listGroups;\r\n        }\r\n\r\n        this._autoSchedule.scheduleTournament(model).subscribe((res) => {\r\n            console.log('res', res);\r\n            this.onSubmit.emit(res);\r\n            this._modalService.dismissAll();\r\n        }, (error) => {\r\n            Swal.fire({\r\n                title: 'Cannot Auto Schedule!',\r\n                text: error.message,\r\n                icon: 'warning',\r\n                confirmButtonText: 'Ok'\r\n            });\r\n        });\r\n    }\r\n\r\n\r\n    closeModal() {\r\n        this.setupScheduleModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n\r\n    clearForm() {\r\n        this.setupScheduleForm.reset();\r\n    }\r\n\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"modalSetupSchedule\">{{ \"Setup Schedule\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"setupScheduleForm\"\r\n  (ngSubmit)=\"onSubmitSetup(setupScheduleModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"setupScheduleForm\"\r\n      [fields]=\"setupScheduleFields\"\r\n      [model]=\"setupScheduleModel\"\r\n      (submit)=\"onSubmitSetup(setupScheduleModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n            [disabled]=\"!setupScheduleForm.valid\"\r\n    >\r\n      {{ 'Plan Match' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}